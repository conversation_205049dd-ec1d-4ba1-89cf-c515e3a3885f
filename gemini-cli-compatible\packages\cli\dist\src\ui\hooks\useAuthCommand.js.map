{"version": 3, "file": "useAuthCommand.js", "sourceRoot": "", "sources": ["../../../../src/ui/hooks/useAuthCommand.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,MAAM,OAAO,CAAC;AACzD,OAAO,EAAkB,YAAY,EAAE,MAAM,0BAA0B,CAAC;AACxE,OAAO,EACL,QAAQ,EAER,yBAAyB,EACzB,eAAe,GAChB,MAAM,sBAAsB,CAAC;AAC9B,OAAO,EAAE,cAAc,EAAE,MAAM,wBAAwB,CAAC;AACxD,OAAO,EACL,mBAAmB,EACnB,uBAAuB,EACvB,kBAAkB,GACnB,MAAM,sBAAsB,CAAC;AAE9B,MAAM,CAAC,MAAM,cAAc,GAAG,CAC5B,QAAwB,EACxB,YAA4C,EAC5C,MAAc,EACd,EAAE;IACF,MAAM,CAAC,gBAAgB,EAAE,mBAAmB,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;IAChE,MAAM,CAAC,cAAc,EAAE,iBAAiB,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;IAE5D,MAAM,cAAc,GAAG,WAAW,CAAC,GAAG,EAAE;QACtC,mBAAmB,CAAC,IAAI,CAAC,CAAC;IAC5B,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,MAAM,CAAC,gBAAgB,EAAE,mBAAmB,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;IAEhE,SAAS,CAAC,GAAG,EAAE;QACb,MAAM,cAAc,GAAG,KAAK,IAAI,EAAE;YAChC,MAAM,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,gBAAgB,CAAC;YAElD,wCAAwC;YACxC,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,wDAAwD;gBACxD,MAAM,iBAAiB,GAAG,MAAM,mBAAmB,EAAE,CAAC;gBACtD,IAAI,iBAAiB,EAAE,CAAC;oBACtB,MAAM,iBAAiB,GAAG,uBAAuB,CAAC,iBAAiB,CAAC,CAAC;oBACrE,IAAI,iBAAiB,EAAE,CAAC;wBACtB,MAAM,eAAe,GAAG,kBAAkB,CAAC,iBAAiB,CAAC,CAAC;wBAC9D,IAAI,CAAC,eAAe,EAAE,CAAC;4BACrB,wCAAwC;4BACxC,QAAQ,CAAC,QAAQ,CACf,YAAY,CAAC,IAAI,EACjB,kBAAkB,EAClB,iBAAiB,CAClB,CAAC;4BACF,OAAO,CAAC,GAAG,CACT,sCAAsC,iBAAiB,mCAAmC,CAC3F,CAAC;4BACF,OAAO;wBACT,CAAC;oBACH,CAAC;gBACH,CAAC;gBAED,uCAAuC;gBACvC,mBAAmB,CAAC,IAAI,CAAC,CAAC;gBAC1B,iBAAiB,CAAC,IAAI,CAAC,CAAC;gBACxB,OAAO;YACT,CAAC;YAED,0CAA0C;YAC1C,IAAI,gBAAgB,EAAE,CAAC;gBACrB,OAAO;YACT,CAAC;YAED,IAAI,CAAC;gBACH,mBAAmB,CAAC,IAAI,CAAC,CAAC;gBAC1B,MAAM,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;gBACnC,OAAO,CAAC,GAAG,CAAC,sBAAsB,QAAQ,IAAI,CAAC,CAAC;YAClD,CAAC;YAAC,OAAO,CAAC,EAAE,CAAC;gBACX,YAAY,CAAC,6BAA6B,eAAe,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;gBAChE,cAAc,EAAE,CAAC;YACnB,CAAC;oBAAS,CAAC;gBACT,mBAAmB,CAAC,KAAK,CAAC,CAAC;YAC7B,CAAC;QACH,CAAC,CAAC;QAEF,KAAK,cAAc,EAAE,CAAC;IACxB,CAAC,EAAE,CAAC,gBAAgB,EAAE,QAAQ,EAAE,MAAM,EAAE,YAAY,EAAE,cAAc,CAAC,CAAC,CAAC;IAEvE,MAAM,gBAAgB,GAAG,WAAW,CAClC,KAAK,EAAE,QAA8B,EAAE,KAAmB,EAAE,EAAE;QAC5D,IAAI,QAAQ,EAAE,CAAC;YACb,MAAM,yBAAyB,EAAE,CAAC;YAElC,QAAQ,CAAC,QAAQ,CAAC,KAAK,EAAE,kBAAkB,EAAE,QAAQ,CAAC,CAAC;YAEvD,IAAI,cAAc,EAAE,CAAC;gBACnB,OAAO,CAAC,GAAG,CAAC,qCAAqC,QAAQ,EAAE,CAAC,CAAC;gBAC7D,iBAAiB,CAAC,KAAK,CAAC,CAAC;YAC3B,CAAC;YAED,IACE,QAAQ,KAAK,QAAQ,CAAC,iBAAiB;gBACvC,MAAM,CAAC,yBAAyB,EAAE,EAClC,CAAC;gBACD,cAAc,EAAE,CAAC;gBACjB,OAAO,CAAC,GAAG,CACT;;;;aAIC,CACF,CAAC;gBACF,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAClB,CAAC;QACH,CAAC;QACD,mBAAmB,CAAC,KAAK,CAAC,CAAC;QAC3B,YAAY,CAAC,IAAI,CAAC,CAAC;IACrB,CAAC,EACD,CAAC,QAAQ,EAAE,YAAY,EAAE,MAAM,EAAE,cAAc,CAAC,CACjD,CAAC;IAEF,MAAM,oBAAoB,GAAG,WAAW,CAAC,GAAG,EAAE;QAC5C,mBAAmB,CAAC,KAAK,CAAC,CAAC;IAC7B,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,OAAO;QACL,gBAAgB;QAChB,cAAc;QACd,gBAAgB;QAChB,gBAAgB;QAChB,oBAAoB;KACrB,CAAC;AACJ,CAAC,CAAC"}