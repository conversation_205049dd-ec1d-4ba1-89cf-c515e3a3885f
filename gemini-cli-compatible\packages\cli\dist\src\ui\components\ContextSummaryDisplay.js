import { jsx as _jsx } from "react/jsx-runtime";
import { Text } from 'ink';
import { Colors } from '../colors.js';
import path from 'path';
export const ContextSummaryDisplay = ({ geminiMdFileCount, contextFileNames, mcpServers, blockedMcpServers, showToolDescriptions, openFiles, }) => {
    const mcpServerCount = Object.keys(mcpServers || {}).length;
    const blockedMcpServerCount = blockedMcpServers?.length || 0;
    if (geminiMdFileCount === 0 &&
        mcpServerCount === 0 &&
        blockedMcpServerCount === 0 &&
        !openFiles?.activeFile) {
        return _jsx(Text, { children: " " }); // Render an empty space to reserve height
    }
    const activeFileText = (() => {
        if (!openFiles?.activeFile) {
            return '';
        }
        return `Open File (${path.basename(openFiles.activeFile)})`;
    })();
    const geminiMdText = (() => {
        if (geminiMdFileCount === 0) {
            return '';
        }
        const allNamesTheSame = new Set(contextFileNames).size < 2;
        const name = allNamesTheSame ? contextFileNames[0] : 'Context';
        return `${geminiMdFileCount} ${name} File${geminiMdFileCount > 1 ? 's' : ''}`;
    })();
    const mcpText = (() => {
        if (mcpServerCount === 0 && blockedMcpServerCount === 0) {
            return '';
        }
        const parts = [];
        if (mcpServerCount > 0) {
            parts.push(`${mcpServerCount} MCP Server${mcpServerCount > 1 ? 's' : ''}`);
        }
        if (blockedMcpServerCount > 0) {
            let blockedText = `${blockedMcpServerCount} Blocked`;
            if (mcpServerCount === 0) {
                blockedText += ` MCP Server${blockedMcpServerCount > 1 ? 's' : ''}`;
            }
            parts.push(blockedText);
        }
        return parts.join(', ');
    })();
    let summaryText = 'Using: ';
    const summaryParts = [];
    if (activeFileText) {
        summaryParts.push(activeFileText);
    }
    if (geminiMdText) {
        summaryParts.push(geminiMdText);
    }
    if (mcpText) {
        summaryParts.push(mcpText);
    }
    summaryText += summaryParts.join(' | ');
    // Add ctrl+t hint when MCP servers are available
    if (mcpServers && Object.keys(mcpServers).length > 0) {
        if (showToolDescriptions) {
            summaryText += ' (ctrl+t to toggle)';
        }
        else {
            summaryText += ' (ctrl+t to view)';
        }
    }
    return _jsx(Text, { color: Colors.Gray, children: summaryText });
};
//# sourceMappingURL=ContextSummaryDisplay.js.map