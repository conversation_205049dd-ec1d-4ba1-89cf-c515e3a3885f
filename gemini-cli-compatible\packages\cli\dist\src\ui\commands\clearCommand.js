/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
import { uiTelemetryService } from '@inkbytefo/s647-core';
import { CommandKind } from './types.js';
export const clearCommand = {
    name: 'clear',
    description: 'clear the screen and conversation history',
    kind: CommandKind.BUILT_IN,
    action: async (context, _args) => {
        const geminiClient = context.services.config?.getGeminiClient();
        if (geminiClient) {
            context.ui.setDebugMessage('Clearing terminal and resetting chat.');
            // If resetChat fails, the exception will propagate and halt the command,
            // which is the correct behavior to signal a failure to the user.
            await geminiClient.resetChat();
        }
        else {
            context.ui.setDebugMessage('Clearing terminal.');
        }
        uiTelemetryService.resetLastPromptTokenCount();
        context.ui.clear();
    },
};
//# sourceMappingURL=clearCommand.js.map