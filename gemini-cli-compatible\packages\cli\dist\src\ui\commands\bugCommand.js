/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
import open from 'open';
import process from 'node:process';
import { CommandKind, } from './types.js';
import { MessageType } from '../types.js';
import { GIT_COMMIT_INFO } from '../../generated/git-commit.js';
import { formatMemoryUsage } from '../utils/formatters.js';
import { getCliVersion } from '../../utils/version.js';
export const bugCommand = {
    name: 'bug',
    description: 'submit a bug report',
    kind: CommandKind.BUILT_IN,
    action: async (context, args) => {
        const bugDescription = (args || '').trim();
        const { config } = context.services;
        const osVersion = `${process.platform} ${process.version}`;
        let sandboxEnv = 'no sandbox';
        if (process.env.SANDBOX && process.env.SANDBOX !== 'sandbox-exec') {
            sandboxEnv = process.env.SANDBOX.replace(/^gemini-(?:code-)?/, '');
        }
        else if (process.env.SANDBOX === 'sandbox-exec') {
            sandboxEnv = `sandbox-exec (${process.env.SEATBELT_PROFILE || 'unknown'})`;
        }
        const modelVersion = config?.getModel() || 'Unknown';
        const cliVersion = await getCliVersion();
        const memoryUsage = formatMemoryUsage(process.memoryUsage().rss);
        const info = `
* **CLI Version:** ${cliVersion}
* **Git Commit:** ${GIT_COMMIT_INFO}
* **Operating System:** ${osVersion}
* **Sandbox Environment:** ${sandboxEnv}
* **Model Version:** ${modelVersion}
* **Memory Usage:** ${memoryUsage}
`;
        let bugReportUrl = 'https://github.com/google-gemini/gemini-cli/issues/new?template=bug_report.yml&title={title}&info={info}';
        const bugCommandSettings = config?.getBugCommand();
        if (bugCommandSettings?.urlTemplate) {
            bugReportUrl = bugCommandSettings.urlTemplate;
        }
        bugReportUrl = bugReportUrl
            .replace('{title}', encodeURIComponent(bugDescription))
            .replace('{info}', encodeURIComponent(info));
        context.ui.addItem({
            type: MessageType.INFO,
            text: `To submit your bug report, please open the following URL in your browser:\n${bugReportUrl}`,
        }, Date.now());
        try {
            await open(bugReportUrl);
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            context.ui.addItem({
                type: MessageType.ERROR,
                text: `Could not open URL in browser: ${errorMessage}`,
            }, Date.now());
        }
    },
};
//# sourceMappingURL=bugCommand.js.map