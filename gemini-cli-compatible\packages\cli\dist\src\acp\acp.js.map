{"version": 3, "file": "acp.js", "sourceRoot": "", "sources": ["../../../src/acp/acp.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAOH,MAAM,OAAO,gBAAgB;IAC3B,WAAW,CAAoB;IAE/B,YACE,KAAgC,EAChC,KAAiC,EACjC,MAAkC;QAElC,IAAI,CAAC,WAAW,GAAG,IAAI,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;IAChE,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,2BAA2B,CAC/B,MAAyC;QAEzC,MAAM,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,6BAA6B,EAAE,MAAM,CAAC,CAAC;IAC5E,CAAC;IAED;;;;;OAKG;IACH,2BAA2B,CACzB,MAAyC;QAEzC,OAAO,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,6BAA6B,EAAE,MAAM,CAAC,CAAC;IAC7E,CAAC;IAED;;;;;;OAMG;IACH,YAAY,CAAC,MAA0B;QACrC,OAAO,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC;IAC9D,CAAC;IAED;;;;;;;OAOG;IACH,KAAK,CAAC,cAAc,CAAC,MAA4B;QAC/C,MAAM,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAAC;IAC/D,CAAC;CACF;AA+BD,MAAM,UAAU;IACd,iBAAiB,GAAiC,IAAI,GAAG,EAAE,CAAC;IAC5D,cAAc,GAAW,CAAC,CAAC;IAC3B,SAAS,CAAI;IACb,UAAU,CAA6B;IACvC,WAAW,GAAkB,OAAO,CAAC,OAAO,EAAE,CAAC;IAC/C,YAAY,CAAc;IAE1B,YACE,QAAW,EACX,SAAqC,EACrC,UAAsC;QAEtC,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAC5B,IAAI,CAAC,YAAY,GAAG,IAAI,WAAW,EAAE,CAAC;QAEtC,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QAC1B,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;IAC5B,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,MAAkC;QAC/C,IAAI,OAAO,GAAG,EAAE,CAAC;QACjB,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;QAClC,IAAI,KAAK,EAAE,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;YACjC,OAAO,IAAI,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;YACnD,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAClC,OAAO,GAAG,KAAK,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC;YAE5B,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;gBACzB,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;gBAEhC,IAAI,WAAW,EAAE,CAAC;oBAChB,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;oBACxC,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;gBAChC,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,OAAmB;QACvC,IAAI,QAAQ,IAAI,OAAO,EAAE,CAAC;YACxB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAChD,OAAO,CAAC,MAAM,EACd,OAAO,CAAC,MAAM,CACf,CAAC;YAEF,MAAM,IAAI,CAAC,YAAY,CAAC;gBACtB,OAAO,EAAE,KAAK;gBACd,EAAE,EAAE,OAAO,CAAC,EAAE;gBACd,GAAG,QAAQ;aACZ,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;QAChC,CAAC;IACH,CAAC;IAED,KAAK,CAAC,sBAAsB,CAC1B,MAAc,EACd,MAAgB;QAEhB,MAAM,UAAU,GAAG,MAAiB,CAAC;QACrC,IAAI,OAAO,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,KAAK,UAAU,EAAE,CAAC;YACrD,OAAO,YAAY,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAC;QACxD,CAAC;QAED,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,CAAC;YACxD,OAAO,EAAE,MAAM,EAAE,MAAM,IAAI,IAAI,EAAE,CAAC;QACpC,CAAC;QAAC,OAAO,KAAc,EAAE,CAAC;YACxB,IAAI,KAAK,YAAY,YAAY,EAAE,CAAC;gBAClC,OAAO,KAAK,CAAC,QAAQ,EAAE,CAAC;YAC1B,CAAC;YAED,IAAI,OAAO,CAAC;YAEZ,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;gBAC3B,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;YAC1B,CAAC;iBAAM,IACL,OAAO,KAAK,KAAK,QAAQ;gBACzB,KAAK,IAAI,IAAI;gBACb,SAAS,IAAI,KAAK;gBAClB,OAAO,KAAK,CAAC,OAAO,KAAK,QAAQ,EACjC,CAAC;gBACD,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;YAC1B,CAAC;YAED,OAAO,YAAY,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,CAAC;QACxD,CAAC;IACH,CAAC;IAED,eAAe,CAAC,QAAqB;QACnC,MAAM,eAAe,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;QAChE,IAAI,eAAe,EAAE,CAAC;YACpB,IAAI,QAAQ,IAAI,QAAQ,EAAE,CAAC;gBACzB,eAAe,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YAC3C,CAAC;iBAAM,IAAI,OAAO,IAAI,QAAQ,EAAE,CAAC;gBAC/B,eAAe,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YACzC,CAAC;YACD,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;QAC7C,CAAC;IACH,CAAC;IAED,KAAK,CAAC,WAAW,CAAY,MAAc,EAAE,MAAY;QACvD,MAAM,EAAE,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QACjC,MAAM,eAAe,GAAG,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACtD,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QACH,MAAM,IAAI,CAAC,YAAY,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,CAAC;QAChE,OAAO,eAAgC,CAAC;IAC1C,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,IAAgB;QACjC,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;QAC5C,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW;aAChC,IAAI,CAAC,KAAK,IAAI,EAAE;YACf,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC;YAC3C,IAAI,CAAC;gBACH,MAAM,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;YACxD,CAAC;oBAAS,CAAC;gBACT,MAAM,CAAC,WAAW,EAAE,CAAC;YACvB,CAAC;QACH,CAAC,CAAC;aACD,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;YACf,sCAAsC;YACtC,OAAO,CAAC,KAAK,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QACL,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;CACF;AAED,MAAM,OAAO,YAAa,SAAQ,KAAK;IAI5B;IAHT,IAAI,CAAwB;IAE5B,YACS,IAAY,EACnB,OAAe,EACf,OAAgB;QAEhB,KAAK,CAAC,OAAO,CAAC,CAAC;QAJR,SAAI,GAAJ,IAAI,CAAQ;QAKnB,IAAI,CAAC,IAAI,GAAG,cAAc,CAAC;QAC3B,IAAI,OAAO,EAAE,CAAC;YACZ,IAAI,CAAC,IAAI,GAAG,EAAE,OAAO,EAAE,CAAC;QAC1B,CAAC;IACH,CAAC;IAED,MAAM,CAAC,UAAU,CAAC,OAAgB;QAChC,OAAO,IAAI,YAAY,CAAC,CAAC,KAAK,EAAE,aAAa,EAAE,OAAO,CAAC,CAAC;IAC1D,CAAC;IAED,MAAM,CAAC,cAAc,CAAC,OAAgB;QACpC,OAAO,IAAI,YAAY,CAAC,CAAC,KAAK,EAAE,iBAAiB,EAAE,OAAO,CAAC,CAAC;IAC9D,CAAC;IAED,MAAM,CAAC,cAAc,CAAC,OAAgB;QACpC,OAAO,IAAI,YAAY,CAAC,CAAC,KAAK,EAAE,kBAAkB,EAAE,OAAO,CAAC,CAAC;IAC/D,CAAC;IAED,MAAM,CAAC,aAAa,CAAC,OAAgB;QACnC,OAAO,IAAI,YAAY,CAAC,CAAC,KAAK,EAAE,gBAAgB,EAAE,OAAO,CAAC,CAAC;IAC7D,CAAC;IAED,MAAM,CAAC,aAAa,CAAC,OAAgB;QACnC,OAAO,IAAI,YAAY,CAAC,CAAC,KAAK,EAAE,gBAAgB,EAAE,OAAO,CAAC,CAAC;IAC7D,CAAC;IAED,QAAQ;QACN,OAAO;YACL,KAAK,EAAE;gBACL,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,IAAI,EAAE,IAAI,CAAC,IAAI;aAChB;SACF,CAAC;IACJ,CAAC;CACF;AAED,iBAAiB;AAEjB,MAAM,CAAC,MAAM,uBAAuB,GAAG,OAAO,CAAC"}