/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
import { AuthType } from '@inkbytefo/s647-core';
export interface ProviderStatus {
    provider: string;
    authType: AuthType;
    available: boolean;
    hasApiKey: boolean;
    apiKeyValid: boolean;
    errorMessage?: string;
    displayName: string;
    description: string;
}
export interface ProviderInfo {
    name: string;
    authType: AuthType;
    envVar: string;
    displayName: string;
    description: string;
    testEndpoint?: string;
}
export declare const PROVIDER_CONFIGS: Record<string, ProviderInfo>;
export declare const getAuthTypeFromProvider: (provider?: string) => AuthType | undefined;
/**
 * Checks if an API key is a placeholder value
 */
export declare const isPlaceholderApiKey: (apiKey: string) => boolean;
/**
 * Validates if an API key has the correct format for a provider
 */
export declare const validateApiKeyFormat: (provider: string, apiKey: string) => boolean;
export declare const detectProviderFromEnvironment: () => string | undefined;
export declare const validateAuthMethod: (authMethod: string) => string | null;
/**
 * Gets the status of all available providers
 */
export declare const getAllProviderStatuses: () => Promise<ProviderStatus[]>;
/**
 * Gets available providers (those with valid API keys)
 */
export declare const getAvailableProviders: () => Promise<ProviderStatus[]>;
/**
 * Suggests the best provider based on available API keys and .env configuration
 */
export declare const suggestBestProvider: () => Promise<string | undefined>;
/**
 * Gets alternative provider suggestions when current provider fails
 */
export declare const getAlternativeProviderSuggestions: (failedProvider: string) => Promise<string[]>;
/**
 * Generates helpful error message with suggestions
 */
export declare const generateAuthErrorMessage: (error: string, authType?: AuthType) => Promise<string>;
