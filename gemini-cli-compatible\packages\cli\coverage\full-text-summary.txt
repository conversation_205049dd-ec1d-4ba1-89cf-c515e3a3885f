--------------------------------|---------|----------|---------|---------|-------------------
File                            | % Stmts | % Branch | % Funcs | % Lines | Uncovered Line #s 
--------------------------------|---------|----------|---------|---------|-------------------
All files                       |       0 |        0 |       0 |       0 |                   
 src                            |       0 |        0 |       0 |       0 |                   
  gemini.tsx                    |       0 |        0 |       0 |       0 | 1-327             
  nonInteractiveCli.ts          |       0 |        0 |       0 |       0 | 1-172             
  validateNonInterActiveAuth.ts |       0 |        0 |       0 |       0 | 1-38              
 src/acp                        |       0 |        0 |       0 |       0 |                   
  acp.ts                        |       0 |        0 |       0 |       0 | 1-275             
  acpPeer.ts                    |       0 |        0 |       0 |       0 | 1-674             
 src/config                     |       0 |        0 |       0 |       0 |                   
  auth.ts                       |       0 |        0 |       0 |       0 | 1-457             
  config.ts                     |       0 |        0 |       0 |       0 | 1-558             
  extension.ts                  |       0 |        0 |       0 |       0 | 1-164             
  sandboxConfig.ts              |       0 |        0 |       0 |       0 | 1-107             
  settings.ts                   |       0 |        0 |       0 |       0 | 1-405             
 src/generated                  |       0 |        0 |       0 |       0 |                   
  git-commit.ts                 |       0 |        0 |       0 |       0 | 1-9               
 src/services                   |       0 |        0 |       0 |       0 |                   
  BuiltinCommandLoader.ts       |       0 |        0 |       0 |       0 | 1-73              
  CommandService.ts             |       0 |        0 |       0 |       0 | 1-83              
  FileCommandLoader.ts          |       0 |        0 |       0 |       0 | 1-171             
  types.ts                      |       0 |        0 |       0 |       0 | 1                 
 src/test-utils                 |       0 |        0 |       0 |       0 |                   
  mockCommandContext.ts         |       0 |        0 |       0 |       0 | 1-96              
 src/ui                         |       0 |        0 |       0 |       0 |                   
  App.tsx                       |       0 |        0 |       0 |       0 | 1-988             
  colors.ts                     |       0 |        0 |       0 |       0 | 1-50              
  constants.ts                  |       0 |        0 |       0 |       0 | 1-17              
  types.ts                      |       0 |        0 |       0 |       0 | 1-165             
 src/ui/commands                |       0 |        0 |       0 |       0 |                   
  aboutCommand.ts               |       0 |        0 |       0 |       0 | 1-44              
  authCommand.ts                |       0 |        0 |       0 |       0 | 1-17              
  bugCommand.ts                 |       0 |        0 |       0 |       0 | 1-83              
  chatCommand.ts                |       0 |        0 |       0 |       0 | 1-206             
  clearCommand.ts               |       0 |        0 |       0 |       0 | 1-29              
  compressCommand.ts            |       0 |        0 |       0 |       0 | 1-78              
  copyCommand.ts                |       0 |        0 |       0 |       0 | 1-67              
  corgiCommand.ts               |       0 |        0 |       0 |       0 | 1-16              
  docsCommand.ts                |       0 |        0 |       0 |       0 | 1-42              
  editorCommand.ts              |       0 |        0 |       0 |       0 | 1-21              
  extensionsCommand.ts          |       0 |        0 |       0 |       0 | 1-46              
  helpCommand.ts                |       0 |        0 |       0 |       0 | 1-21              
  ideCommand.ts                 |       0 |        0 |       0 |       0 | 1-169             
  mcpCommand.ts                 |       0 |        0 |       0 |       0 | 1-428             
  memoryCommand.ts              |       0 |        0 |       0 |       0 | 1-114             
  privacyCommand.ts             |       0 |        0 |       0 |       0 | 1-17              
  quitCommand.ts                |       0 |        0 |       0 |       0 | 1-36              
  restoreCommand.ts             |       0 |        0 |       0 |       0 | 1-157             
  statsCommand.ts               |       0 |        0 |       0 |       0 | 1-70              
  themeCommand.ts               |       0 |        0 |       0 |       0 | 1-17              
  toolsCommand.ts               |       0 |        0 |       0 |       0 | 1-71              
  types.ts                      |       0 |        0 |       0 |       0 | 1-121             
 src/ui/components              |       0 |        0 |       0 |       0 |                   
  AboutBox.tsx                  |       0 |        0 |       0 |       0 | 1-118             
  AsciiArt.ts                   |       0 |        0 |       0 |       0 | 1-18              
  AuthDialog.tsx                |       0 |        0 |       0 |       0 | 1-246             
  AuthInProgress.tsx            |       0 |        0 |       0 |       0 | 1-57              
  AutoAcceptIndicator.tsx       |       0 |        0 |       0 |       0 | 1-47              
  ConsoleSummaryDisplay.tsx     |       0 |        0 |       0 |       0 | 1-35              
  ContextSummaryDisplay.tsx     |       0 |        0 |       0 |       0 | 1-105             
  DetailedMessagesDisplay.tsx   |       0 |        0 |       0 |       0 | 1-82              
  EditorSettingsDialog.tsx      |       0 |        0 |       0 |       0 | 1-168             
  Footer.tsx                    |       0 |        0 |       0 |       0 | 1-121             
  GeminiRespondingSpinner.tsx   |       0 |        0 |       0 |       0 | 1-34              
  Header.tsx                    |       0 |        0 |       0 |       0 | 1-63              
  Help.tsx                      |       0 |        0 |       0 |       0 | 1-154             
  HistoryItemDisplay.tsx        |       0 |        0 |       0 |       0 | 1-91              
  InputPrompt.tsx               |       0 |        0 |       0 |       0 | 1-553             
  LoadingIndicator.tsx          |       0 |        0 |       0 |       0 | 1-61              
  MemoryUsageDisplay.tsx        |       0 |        0 |       0 |       0 | 1-36              
  ModelStatsDisplay.tsx         |       0 |        0 |       0 |       0 | 1-197             
  SessionSummaryDisplay.tsx     |       0 |        0 |       0 |       0 | 1-17              
  ShellModeIndicator.tsx        |       0 |        0 |       0 |       0 | 1-17              
  ShowMoreLines.tsx             |       0 |        0 |       0 |       0 | 1-40              
  StatsDisplay.tsx              |       0 |        0 |       0 |       0 | 1-259             
  SuggestionsDisplay.tsx        |       0 |        0 |       0 |       0 | 1-93              
  ThemeDialog.tsx               |       0 |        0 |       0 |       0 | 1-311             
  Tips.tsx                      |       0 |        0 |       0 |       0 | 1-45              
  ToolStatsDisplay.tsx          |       0 |        0 |       0 |       0 | 1-208             
  UpdateNotification.tsx        |       0 |        0 |       0 |       0 | 1-22              
 src/ui/components/messages     |       0 |        0 |       0 |       0 |                   
  CompressionMessage.tsx        |       0 |        0 |       0 |       0 | 1-49              
  DiffRenderer.tsx              |       0 |        0 |       0 |       0 | 1-317             
  ErrorMessage.tsx              |       0 |        0 |       0 |       0 | 1-31              
  GeminiMessage.tsx             |       0 |        0 |       0 |       0 | 1-43              
  GeminiMessageContent.tsx      |       0 |        0 |       0 |       0 | 1-43              
  InfoMessage.tsx               |       0 |        0 |       0 |       0 | 1-31              
  ToolConfirmationMessage.tsx   |       0 |        0 |       0 |       0 | 1-250             
  ToolGroupMessage.tsx          |       0 |        0 |       0 |       0 | 1-126             
  ToolMessage.tsx               |       0 |        0 |       0 |       0 | 1-193             
  UserMessage.tsx               |       0 |        0 |       0 |       0 | 1-39              
  UserShellMessage.tsx          |       0 |        0 |       0 |       0 | 1-25              
 src/ui/components/shared       |       0 |        0 |       0 |       0 |                   
  MaxSizedBox.tsx               |       0 |        0 |       0 |       0 | 1-624             
  RadioButtonSelect.tsx         |       0 |        0 |       0 |       0 | 1-232             
  text-buffer.ts                |       0 |        0 |       0 |       0 | 1-1277            
 src/ui/contexts                |       0 |        0 |       0 |       0 |                   
  OverflowContext.tsx           |       0 |        0 |       0 |       0 | 1-87              
  SessionContext.tsx            |       0 |        0 |       0 |       0 | 1-138             
  StreamingContext.tsx          |       0 |        0 |       0 |       0 | 1-22              
 src/ui/editors                 |       0 |        0 |       0 |       0 |                   
  editorSettingsManager.ts      |       0 |        0 |       0 |       0 | 1-71              
 src/ui/hooks                   |       0 |        0 |       0 |       0 |                   
  atCommandProcessor.ts         |       0 |        0 |       0 |       0 | 1-468             
  shellCommandProcessor.ts      |       0 |        0 |       0 |       0 | 1-393             
  slashCommandProcessor.ts      |       0 |        0 |       0 |       0 | 1-358             
  useAuthCommand.ts             |       0 |        0 |       0 |       0 | 1-133             
  useAutoAcceptIndicator.ts     |       0 |        0 |       0 |       0 | 1-49              
  useBracketedPaste.ts          |       0 |        0 |       0 |       0 | 1-37              
  useCompletion.ts              |       0 |        0 |       0 |       0 | 1-570             
  useConsoleMessages.ts         |       0 |        0 |       0 |       0 | 1-89              
  useEditorSettings.ts          |       0 |        0 |       0 |       0 | 1-75              
  useFocus.ts                   |       0 |        0 |       0 |       0 | 1-48              
  useGeminiStream.ts            |       0 |        0 |       0 |       0 | 1-954             
  useGitBranchName.ts           |       0 |        0 |       0 |       0 | 1-79              
  useHistoryManager.ts          |       0 |        0 |       0 |       0 | 1-111             
  useInputHistory.ts            |       0 |        0 |       0 |       0 | 1-111             
  useKeypress.ts                |       0 |        0 |       0 |       0 | 1-184             
  useLoadingIndicator.ts        |       0 |        0 |       0 |       0 | 1-57              
  useLogger.ts                  |       0 |        0 |       0 |       0 | 1-32              
  usePhraseCycler.ts            |       0 |        0 |       0 |       0 | 1-200             
  usePrivacySettings.ts         |       0 |        0 |       0 |       0 | 1-139             
  useReactToolScheduler.ts      |       0 |        0 |       0 |       0 | 1-312             
  useRefreshMemoryCommand.ts    |       0 |        0 |       0 |       0 | 1-7               
  useShellHistory.ts            |       0 |        0 |       0 |       0 | 1-103             
  useShowMemoryCommand.ts       |       0 |        0 |       0 |       0 | 1-75              
  useStateAndRef.ts             |       0 |        0 |       0 |       0 | 1-36              
  useTerminalSize.ts            |       0 |        0 |       0 |       0 | 1-32              
  useThemeCommand.ts            |       0 |        0 |       0 |       0 | 1-110             
  useTimer.ts                   |       0 |        0 |       0 |       0 | 1-65              
 src/ui/privacy                 |       0 |        0 |       0 |       0 |                   
  CloudFreePrivacyNotice.tsx    |       0 |        0 |       0 |       0 | 1-113             
  CloudPaidPrivacyNotice.tsx    |       0 |        0 |       0 |       0 | 1-55              
  GeminiPrivacyNotice.tsx       |       0 |        0 |       0 |       0 | 1-58              
  PrivacyNotice.tsx             |       0 |        0 |       0 |       0 | 1-41              
 src/ui/themes                  |       0 |        0 |       0 |       0 |                   
  ansi-light.ts                 |       0 |        0 |       0 |       0 | 1-146             
  ansi.ts                       |       0 |        0 |       0 |       0 | 1-155             
  atom-one-dark.ts              |       0 |        0 |       0 |       0 | 1-143             
  ayu-light.ts                  |       0 |        0 |       0 |       0 | 1-135             
  ayu.ts                        |       0 |        0 |       0 |       0 | 1-109             
  color-utils.ts                |       0 |        0 |       0 |       0 | 1-231             
  default-light.ts              |       0 |        0 |       0 |       0 | 1-106             
  default.ts                    |       0 |        0 |       0 |       0 | 1-149             
  dracula.ts                    |       0 |        0 |       0 |       0 | 1-120             
  github-dark.ts                |       0 |        0 |       0 |       0 | 1-143             
  github-light.ts               |       0 |        0 |       0 |       0 | 1-145             
  googlecode.ts                 |       0 |        0 |       0 |       0 | 1-142             
  no-color.ts                   |       0 |        0 |       0 |       0 | 1-91              
  shades-of-purple.ts           |       0 |        0 |       0 |       0 | 1-348             
  theme-manager.ts              |       0 |        0 |       0 |       0 | 1-218             
  theme.ts                      |       0 |        0 |       0 |       0 | 1-388             
  xcode.ts                      |       0 |        0 |       0 |       0 | 1-150             
 src/ui/utils                   |       0 |        0 |       0 |       0 |                   
  CodeColorizer.tsx             |       0 |        0 |       0 |       0 | 1-185             
  ConsolePatcher.ts             |       0 |        0 |       0 |       0 | 1-61              
  InlineMarkdownRenderer.tsx    |       0 |        0 |       0 |       0 | 1-162             
  MarkdownDisplay.tsx           |       0 |        0 |       0 |       0 | 1-409             
  TableRenderer.tsx             |       0 |        0 |       0 |       0 | 1-159             
  clipboardUtils.ts             |       0 |        0 |       0 |       0 | 1-149             
  commandUtils.ts               |       0 |        0 |       0 |       0 | 1-82              
  computeStats.ts               |       0 |        0 |       0 |       0 | 1-84              
  displayUtils.ts               |       0 |        0 |       0 |       0 | 1-32              
  errorParsing.ts               |       0 |        0 |       0 |       0 | 1-164             
  formatters.ts                 |       0 |        0 |       0 |       0 | 1-63              
  markdownUtilities.ts          |       0 |        0 |       0 |       0 | 1-125             
  textUtils.ts                  |       0 |        0 |       0 |       0 | 1-69              
  updateCheck.ts                |       0 |        0 |       0 |       0 | 1-45              
 src/utils                      |       0 |        0 |       0 |       0 |                   
  cleanup.ts                    |       0 |        0 |       0 |       0 | 1-36              
  package.ts                    |       0 |        0 |       0 |       0 | 1-38              
  readStdin.ts                  |       0 |        0 |       0 |       0 | 1-39              
  sandbox.ts                    |       0 |        0 |       0 |       0 | 1-871             
  startupWarnings.ts            |       0 |        0 |       0 |       0 | 1-40              
  userStartupWarnings.ts        |       0 |        0 |       0 |       0 | 1-69              
  version.ts                    |       0 |        0 |       0 |       0 | 1-12              
--------------------------------|---------|----------|---------|---------|-------------------
