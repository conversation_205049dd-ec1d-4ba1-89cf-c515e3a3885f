{"version": 3, "file": "compressCommand.js", "sourceRoot": "", "sources": ["../../../../src/ui/commands/compressCommand.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAA0B,WAAW,EAAE,MAAM,aAAa,CAAC;AAClE,OAAO,EAAE,WAAW,EAAgB,MAAM,YAAY,CAAC;AAEvD,MAAM,CAAC,MAAM,eAAe,GAAiB;IAC3C,IAAI,EAAE,UAAU;IAChB,QAAQ,EAAE,CAAC,WAAW,CAAC;IACvB,WAAW,EAAE,wDAAwD;IACrE,IAAI,EAAE,WAAW,CAAC,QAAQ;IAC1B,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,EAAE;QACxB,MAAM,EAAE,EAAE,EAAE,GAAG,OAAO,CAAC;QACvB,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;YACnB,EAAE,CAAC,OAAO,CACR;gBACE,IAAI,EAAE,WAAW,CAAC,KAAK;gBACvB,IAAI,EAAE,4DAA4D;aACnE,EACD,IAAI,CAAC,GAAG,EAAE,CACX,CAAC;YACF,OAAO;QACT,CAAC;QAED,MAAM,cAAc,GAA2B;YAC7C,IAAI,EAAE,WAAW,CAAC,WAAW;YAC7B,WAAW,EAAE;gBACX,SAAS,EAAE,IAAI;gBACf,kBAAkB,EAAE,IAAI;gBACxB,aAAa,EAAE,IAAI;aACpB;SACF,CAAC;QAEF,IAAI,CAAC;YACH,EAAE,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;YAClC,MAAM,QAAQ,GAAG,YAAY,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC;YAC1C,MAAM,UAAU,GAAG,MAAM,OAAO,CAAC,QAAQ,CAAC,MAAM;gBAC9C,EAAE,eAAe,EAAE;gBACnB,EAAE,eAAe,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;YACpC,IAAI,UAAU,EAAE,CAAC;gBACf,EAAE,CAAC,OAAO,CACR;oBACE,IAAI,EAAE,WAAW,CAAC,WAAW;oBAC7B,WAAW,EAAE;wBACX,SAAS,EAAE,KAAK;wBAChB,kBAAkB,EAAE,UAAU,CAAC,kBAAkB;wBACjD,aAAa,EAAE,UAAU,CAAC,aAAa;qBACxC;iBACwB,EAC3B,IAAI,CAAC,GAAG,EAAE,CACX,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,EAAE,CAAC,OAAO,CACR;oBACE,IAAI,EAAE,WAAW,CAAC,KAAK;oBACvB,IAAI,EAAE,kCAAkC;iBACzC,EACD,IAAI,CAAC,GAAG,EAAE,CACX,CAAC;YACJ,CAAC;QACH,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,EAAE,CAAC,OAAO,CACR;gBACE,IAAI,EAAE,WAAW,CAAC,KAAK;gBACvB,IAAI,EAAE,oCACJ,CAAC,YAAY,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAC3C,EAAE;aACH,EACD,IAAI,CAAC,GAAG,EAAE,CACX,CAAC;QACJ,CAAC;gBAAS,CAAC;YACT,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QAC1B,CAAC;IACH,CAAC;CACF,CAAC"}