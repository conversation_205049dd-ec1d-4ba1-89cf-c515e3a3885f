{"version": 3, "file": "ideCommand.js", "sourceRoot": "", "sources": ["../../../../src/ui/commands/ideCommand.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,aAAa,EAAE,MAAM,KAAK,CAAC;AACpC,OAAO,EAEL,oBAAoB,EACpB,kBAAkB,EAClB,eAAe,EACf,iBAAiB,EACjB,eAAe,GAChB,MAAM,sBAAsB,CAAC;AAC9B,OAAO,EAIL,WAAW,GACZ,MAAM,YAAY,CAAC;AACpB,OAAO,KAAK,aAAa,MAAM,eAAe,CAAC;AAC/C,OAAO,KAAK,OAAO,MAAM,SAAS,CAAC;AACnC,OAAO,EAAE,IAAI,EAAE,MAAM,MAAM,CAAC;AAC5B,OAAO,KAAK,IAAI,MAAM,MAAM,CAAC;AAE7B,MAAM,cAAc,GAAG,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC;AAC1E,MAAM,iCAAiC,GAAG,sBAAsB,CAAC;AAEjE,SAAS,iBAAiB;IACxB,IAAI,CAAC;QACH,aAAa,CAAC,QAAQ,CACpB,OAAO,CAAC,QAAQ,KAAK,OAAO;YAC1B,CAAC,CAAC,aAAa,cAAc,EAAE;YAC/B,CAAC,CAAC,cAAc,cAAc,EAAE,EAClC,EAAE,KAAK,EAAE,QAAQ,EAAE,CACpB,CAAC;QACF,OAAO,IAAI,CAAC;IACd,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC;AAED,MAAM,CAAC,MAAM,UAAU,GAAG,CAAC,MAAqB,EAAuB,EAAE;IACvE,IAAI,CAAC,MAAM,EAAE,UAAU,EAAE,EAAE,CAAC;QAC1B,OAAO,IAAI,CAAC;IACd,CAAC;IAED,OAAO;QACL,IAAI,EAAE,KAAK;QACX,WAAW,EAAE,wBAAwB;QACrC,IAAI,EAAE,WAAW,CAAC,QAAQ;QAC1B,WAAW,EAAE;YACX;gBACE,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,iCAAiC;gBAC9C,IAAI,EAAE,WAAW,CAAC,QAAQ;gBAC1B,MAAM,EAAE,CAAC,QAAwB,EAA4B,EAAE;oBAC7D,MAAM,MAAM,GAAG,kBAAkB,CAAC,eAAe,CAAC,CAAC;oBACnD,MAAM,cAAc,GAAG,oBAAoB,EAAE,CAAC;oBAC9C,QAAQ,MAAM,EAAE,CAAC;wBACf,KAAK,eAAe,CAAC,SAAS;4BAC5B,OAAO;gCACL,IAAI,EAAE,SAAS;gCACf,WAAW,EAAE,MAAM;gCACnB,OAAO,EAAE,cAAc;6BACxB,CAAC;wBACJ,KAAK,eAAe,CAAC,UAAU;4BAC7B,OAAO;gCACL,IAAI,EAAE,SAAS;gCACf,WAAW,EAAE,MAAM;gCACnB,OAAO,EAAE,oBAAoB;6BAC9B,CAAC;wBACJ,KAAK,eAAe,CAAC,YAAY,CAAC;wBAClC;4BACE,IAAI,cAAc,KAAK,iBAAiB,CAAC,WAAW,EAAE,CAAC;gCACrD,OAAO;oCACL,IAAI,EAAE,SAAS;oCACf,WAAW,EAAE,MAAM;oCACnB,OAAO,EAAE,oBAAoB;iCAC9B,CAAC;4BACJ,CAAC;iCAAM,CAAC;gCACN,OAAO;oCACL,IAAI,EAAE,SAAS;oCACf,WAAW,EAAE,OAAO;oCACpB,OAAO,EAAE,iBAAiB;iCAC3B,CAAC;4BACJ,CAAC;oBACL,CAAC;gBACH,CAAC;aACF;YACD;gBACE,IAAI,EAAE,SAAS;gBACf,WAAW,EAAE,8CAA8C;gBAC3D,IAAI,EAAE,WAAW,CAAC,QAAQ;gBAC1B,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,EAAE;oBACxB,IAAI,CAAC,iBAAiB,EAAE,EAAE,CAAC;wBACzB,OAAO,CAAC,EAAE,CAAC,OAAO,CAChB;4BACE,IAAI,EAAE,OAAO;4BACb,IAAI,EAAE,8BAA8B,cAAc,2BAA2B;yBAC9E,EACD,IAAI,CAAC,GAAG,EAAE,CACX,CAAC;wBACF,OAAO;oBACT,CAAC;oBAED,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;oBAC/D,wEAAwE;oBACxE,IAAI,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC,CAAC;oBAC1D,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;wBAC3B,2DAA2D;wBAC3D,oEAAoE;wBACpE,gDAAgD;wBAChD,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CACvB,SAAS,EACT,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,iCAAiC,EACjC,QAAQ,CACT,CAAC;wBACF,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;oBACjC,CAAC;oBACD,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;wBAC3B,OAAO,CAAC,EAAE,CAAC,OAAO,CAChB;4BACE,IAAI,EAAE,OAAO;4BACb,IAAI,EAAE,sFAAsF;yBAC7F,EACD,IAAI,CAAC,GAAG,EAAE,CACX,CAAC;wBACF,OAAO;oBACT,CAAC;oBAED,MAAM,QAAQ,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;oBAC9B,MAAM,OAAO,GAAG,GAAG,cAAc,wBAAwB,QAAQ,UAAU,CAAC;oBAC5E,OAAO,CAAC,EAAE,CAAC,OAAO,CAChB;wBACE,IAAI,EAAE,MAAM;wBACZ,IAAI,EAAE,2CAA2C;qBAClD,EACD,IAAI,CAAC,GAAG,EAAE,CACX,CAAC;oBACF,IAAI,CAAC;wBACH,aAAa,CAAC,QAAQ,CAAC,OAAO,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,CAAC;wBACnD,OAAO,CAAC,EAAE,CAAC,OAAO,CAChB;4BACE,IAAI,EAAE,MAAM;4BACZ,IAAI,EAAE,oGAAoG;yBAC3G,EACD,IAAI,CAAC,GAAG,EAAE,CACX,CAAC;oBACJ,CAAC;oBAAC,OAAO,MAAM,EAAE,CAAC;wBAChB,OAAO,CAAC,EAAE,CAAC,OAAO,CAChB;4BACE,IAAI,EAAE,OAAO;4BACb,IAAI,EAAE,gDAAgD;yBACvD,EACD,IAAI,CAAC,GAAG,EAAE,CACX,CAAC;oBACJ,CAAC;gBACH,CAAC;aACF;SACF;KACF,CAAC;AACJ,CAAC,CAAC"}