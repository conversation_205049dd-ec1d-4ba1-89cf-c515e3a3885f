{"version": 3, "file": "AuthDialog.js", "sourceRoot": "", "sources": ["../../../../src/ui/components/AuthDialog.tsx"], "names": [], "mappings": ";AAAA;;;;GAIG;AAEH,OAAc,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,OAAO,CAAC;AACnD,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,KAAK,CAAC;AAC1C,OAAO,EAAE,MAAM,EAAE,MAAM,cAAc,CAAC;AACtC,OAAO,EAAE,iBAAiB,EAAE,MAAM,+BAA+B,CAAC;AAClE,OAAO,EAAkB,YAAY,EAAE,MAAM,0BAA0B,CAAC;AACxE,OAAO,EAAE,QAAQ,EAAE,MAAM,sBAAsB,CAAC;AAChD,OAAO,EACL,kBAAkB,EAClB,sBAAsB,EAEtB,mBAAmB,GACpB,MAAM,sBAAsB,CAAC;AAQ9B,SAAS,oBAAoB,CAC3B,eAAmC;IAEnC,IACE,eAAe;QACf,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC,eAA2B,CAAC,EAC7D,CAAC;QACD,OAAO,eAA2B,CAAC;IACrC,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED,MAAM,UAAU,UAAU,CAAC,EACzB,QAAQ,EACR,QAAQ,EACR,mBAAmB,GACH;IAChB,MAAM,CAAC,YAAY,EAAE,eAAe,CAAC,GAAG,QAAQ,CAC9C,mBAAmB,IAAI,IAAI,CAC5B,CAAC;IACF,MAAM,CAAC,gBAAgB,EAAE,mBAAmB,CAAC,GAAG,QAAQ,CACtD,EAAE,CACH,CAAC;IACF,MAAM,CAAC,OAAO,EAAE,UAAU,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;IAC7C,MAAM,CAAC,iBAAiB,EAAE,oBAAoB,CAAC,GAAG,QAAQ,EAEvD,CAAC;IAEJ,SAAS,CAAC,GAAG,EAAE;QACb,MAAM,oBAAoB,GAAG,KAAK,IAAI,EAAE;YACtC,IAAI,CAAC;gBACH,MAAM,QAAQ,GAAG,MAAM,sBAAsB,EAAE,CAAC;gBAChD,mBAAmB,CAAC,QAAQ,CAAC,CAAC;gBAE9B,MAAM,SAAS,GAAG,MAAM,mBAAmB,EAAE,CAAC;gBAC9C,oBAAoB,CAAC,SAAS,CAAC,CAAC;gBAEhC,gDAAgD;gBAChD,IAAI,OAAO,CAAC,GAAG,CAAC,eAAe,IAAI,SAAS,EAAE,CAAC;oBAC7C,eAAe,CACb,gCAAgC,SAAS,iCAAiC,CAC3E,CAAC;gBACJ,CAAC;qBAAM,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC;oBAC7C,MAAM,cAAc,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC;oBAClE,eAAe,CACb,SAAS,cAAc,6CAA6C,CACrE,CAAC;gBACJ,CAAC;qBAAM,CAAC;oBACN,eAAe,CACb,sFAAsF,CACvF,CAAC;gBACJ,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAe,CAAC,uCAAuC,KAAK,EAAE,CAAC,CAAC;YAClE,CAAC;oBAAS,CAAC;gBACT,UAAU,CAAC,KAAK,CAAC,CAAC;YACpB,CAAC;QACH,CAAC,CAAC;QAEF,oBAAoB,EAAE,CAAC;IACzB,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,MAAM,KAAK,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;QAC5C,MAAM,UAAU,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;QAEzE,OAAO;YACL,KAAK,EAAE,GAAG,UAAU,IAAI,MAAM,CAAC,WAAW,EAAE;YAC5C,KAAK,EAAE,MAAM,CAAC,QAAQ;YACtB,WAAW,EAAE,MAAM,CAAC,SAAS;gBAC3B,CAAC,CAAC,MAAM,CAAC,WAAW;gBACpB,CAAC,CAAC,MAAM,CAAC,YAAY,IAAI,eAAe;YAC1C,SAAS,EAAE,MAAM,CAAC,SAAS;YAC3B,WAAW,EAAE,MAAM,CAAC,QAAQ,KAAK,iBAAiB;SACnD,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,MAAM,gBAAgB,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,EAAE,EAAE;QAChD,gDAAgD;QAChD,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACrB,OAAO,IAAI,CAAC;QACd,CAAC;QAED,sCAAsC;QACtC,IAAI,QAAQ,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC;YACrC,OAAO,IAAI,CAAC,KAAK,KAAK,QAAQ,CAAC,MAAM,CAAC,gBAAgB,CAAC;QACzD,CAAC;QAED,4BAA4B;QAC5B,MAAM,eAAe,GAAG,oBAAoB,CAC1C,OAAO,CAAC,GAAG,CAAC,wBAAwB,CACrC,CAAC;QACF,IAAI,eAAe,EAAE,CAAC;YACpB,OAAO,IAAI,CAAC,KAAK,KAAK,eAAe,CAAC;QACxC,CAAC;QAED,sDAAsD;QACtD,OAAO,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,KAAK,KAAK,QAAQ,CAAC,iBAAiB,CAAC;IACrE,CAAC,CAAC,CAAC;IAEH,MAAM,gBAAgB,GAAG,CAAC,UAAoB,EAAE,EAAE;QAChD,MAAM,KAAK,GAAG,kBAAkB,CAAC,UAAU,CAAC,CAAC;QAC7C,IAAI,KAAK,EAAE,CAAC;YACV,eAAe,CAAC,KAAK,CAAC,CAAC;QACzB,CAAC;aAAM,CAAC;YACN,eAAe,CAAC,IAAI,CAAC,CAAC;YACtB,QAAQ,CAAC,UAAU,EAAE,YAAY,CAAC,IAAI,CAAC,CAAC;QAC1C,CAAC;IACH,CAAC,CAAC;IAEF,QAAQ,CAAC,CAAC,MAAM,EAAE,GAAG,EAAE,EAAE;QACvB,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC;YACf,6CAA6C;YAC7C,iDAAiD;YACjD,IAAI,YAAY,EAAE,CAAC;gBACjB,OAAO;YACT,CAAC;YACD,IAAI,QAAQ,CAAC,MAAM,CAAC,gBAAgB,KAAK,SAAS,EAAE,CAAC;gBACnD,2CAA2C;gBAC3C,eAAe,CACb,wEAAwE,CACzE,CAAC;gBACF,OAAO;YACT,CAAC;YACD,QAAQ,CAAC,SAAS,EAAE,YAAY,CAAC,IAAI,CAAC,CAAC;QACzC,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,IAAI,OAAO,EAAE,CAAC;QACZ,OAAO,CACL,MAAC,GAAG,IACF,WAAW,EAAC,OAAO,EACnB,WAAW,EAAE,MAAM,CAAC,IAAI,EACxB,aAAa,EAAC,QAAQ,EACtB,OAAO,EAAE,CAAC,EACV,KAAK,EAAC,MAAM,aAEZ,KAAC,IAAI,IAAC,IAAI,2CAA4B,EACtC,KAAC,GAAG,IAAC,SAAS,EAAE,CAAC,YACf,KAAC,IAAI,+DAAoD,GACrD,IACF,CACP,CAAC;IACJ,CAAC;IAED,OAAO,CACL,MAAC,GAAG,IACF,WAAW,EAAC,OAAO,EACnB,WAAW,EAAE,MAAM,CAAC,IAAI,EACxB,aAAa,EAAC,QAAQ,EACtB,OAAO,EAAE,CAAC,EACV,KAAK,EAAC,MAAM,aAEZ,KAAC,IAAI,IAAC,IAAI,2CAA4B,EACtC,KAAC,GAAG,IAAC,SAAS,EAAE,CAAC,YACf,KAAC,IAAI,+DAAoD,GACrD,EAGN,KAAC,GAAG,IAAC,SAAS,EAAE,CAAC,EAAE,aAAa,EAAC,QAAQ,YACvC,KAAC,IAAI,IAAC,QAAQ,qFAAuD,GACjE,EAEN,KAAC,GAAG,IAAC,SAAS,EAAE,CAAC,YACf,KAAC,iBAAiB,IAChB,KAAK,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;wBAC1B,KAAK,EAAE,IAAI,CAAC,KAAK;wBACjB,KAAK,EAAE,IAAI,CAAC,KAAK;qBAClB,CAAC,CAAC,EACH,YAAY,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,gBAAgB,CAAC,EAC3C,QAAQ,EAAE,gBAAgB,EAC1B,SAAS,EAAE,IAAI,GACf,GACE,EAGN,KAAC,GAAG,IAAC,SAAS,EAAE,CAAC,EAAE,aAAa,EAAC,QAAQ,YACtC,KAAK,CAAC,GAAG,CACR,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CACd,KAAK,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,gBAAgB,CAAC,IAAI,CACzC,MAAC,GAAG,IAAkB,aAAa,EAAC,QAAQ,aAC1C,KAAC,IAAI,IAAC,QAAQ,kBAAE,IAAI,CAAC,WAAW,GAAQ,EACvC,CAAC,IAAI,CAAC,SAAS;4BACd,IAAI,CAAC,KAAK,KAAK,QAAQ,CAAC,iBAAiB,IAAI,CAC3C,KAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,YAAY,gFAEzB,CACR,KAPK,IAAI,CAAC,KAAK,CAQd,CACP,CACJ,GACG,EAEL,YAAY,IAAI,CACf,KAAC,GAAG,IAAC,SAAS,EAAE,CAAC,YACf,KAAC,IAAI,IACH,KAAK,EACH,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC;wBAC5B,CAAC,CAAC,MAAM,CAAC,WAAW;wBACpB,CAAC,CAAC,MAAM,CAAC,SAAS,YAGrB,YAAY,GACR,GACH,CACP,EACD,KAAC,GAAG,IAAC,SAAS,EAAE,CAAC,YACf,KAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,IAAI,sCAA8B,GAClD,EACN,KAAC,GAAG,IAAC,SAAS,EAAE,CAAC,YACf,KAAC,IAAI,sEAA2D,GAC5D,EACN,KAAC,GAAG,IAAC,SAAS,EAAE,CAAC,YACf,KAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,UAAU,YAE1B,2EAA2E,GAExE,GACH,IACF,CACP,CAAC;AACJ,CAAC"}