{"version": 3, "file": "restoreCommand.js", "sourceRoot": "", "sources": ["../../../../src/ui/commands/restoreCommand.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,KAAK,EAAE,MAAM,aAAa,CAAC;AAClC,OAAO,IAAI,MAAM,MAAM,CAAC;AACxB,OAAO,EAIL,WAAW,GACZ,MAAM,YAAY,CAAC;AAGpB,KAAK,UAAU,aAAa,CAC1B,OAAuB,EACvB,IAAY;IAEZ,MAAM,EAAE,QAAQ,EAAE,EAAE,EAAE,GAAG,OAAO,CAAC;IACjC,MAAM,EAAE,MAAM,EAAE,GAAG,EAAE,UAAU,EAAE,GAAG,QAAQ,CAAC;IAC7C,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,GAAG,EAAE,CAAC;IAEpC,MAAM,aAAa,GAAG,MAAM,EAAE,iBAAiB,EAAE;QAC/C,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAAE,EAAE,aAAa,CAAC;QACtD,CAAC,CAAC,SAAS,CAAC;IAEd,IAAI,CAAC,aAAa,EAAE,CAAC;QACnB,OAAO;YACL,IAAI,EAAE,SAAS;YACf,WAAW,EAAE,OAAO;YACpB,OAAO,EAAE,iDAAiD;SAC3D,CAAC;IACJ,CAAC;IAED,IAAI,CAAC;QACH,wDAAwD;QACxD,MAAM,EAAE,CAAC,KAAK,CAAC,aAAa,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;QACnD,MAAM,KAAK,GAAG,MAAM,EAAE,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;QAC9C,MAAM,SAAS,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;QAEjE,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC3B,OAAO;oBACL,IAAI,EAAE,SAAS;oBACf,WAAW,EAAE,MAAM;oBACnB,OAAO,EAAE,iCAAiC;iBAC3C,CAAC;YACJ,CAAC;YACD,MAAM,cAAc,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;gBAC5C,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBACnC,IAAI,UAAU,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;oBAC3B,OAAO,IAAI,CAAC;gBACd,CAAC;gBACD,UAAU,CAAC,GAAG,EAAE,CAAC;gBACjB,OAAO,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC9B,CAAC,CAAC,CAAC;YACH,MAAM,QAAQ,GAAG,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC3C,OAAO;gBACL,IAAI,EAAE,SAAS;gBACf,WAAW,EAAE,MAAM;gBACnB,OAAO,EAAE,uCAAuC,QAAQ,EAAE;aAC3D,CAAC;QACJ,CAAC;QAED,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,OAAO,CAAC;QAEpE,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;YACtC,OAAO;gBACL,IAAI,EAAE,SAAS;gBACf,WAAW,EAAE,OAAO;gBACpB,OAAO,EAAE,mBAAmB,YAAY,EAAE;aAC3C,CAAC;QACJ,CAAC;QAED,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,YAAY,CAAC,CAAC;QACxD,MAAM,IAAI,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QAClD,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAEtC,IAAI,YAAY,CAAC,OAAO,EAAE,CAAC;YACzB,IAAI,CAAC,WAAW,EAAE,CAAC;gBACjB,yBAAyB;gBACzB,OAAO;oBACL,IAAI,EAAE,SAAS;oBACf,WAAW,EAAE,OAAO;oBACpB,OAAO,EAAE,wCAAwC;iBAClD,CAAC;YACJ,CAAC;YACD,WAAW,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;QACpC,CAAC;QAED,IAAI,YAAY,CAAC,aAAa,EAAE,CAAC;YAC/B,MAAM,MAAM,EAAE,eAAe,EAAE,EAAE,UAAU,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;QAC1E,CAAC;QAED,IAAI,YAAY,CAAC,UAAU,EAAE,CAAC;YAC5B,MAAM,UAAU,EAAE,0BAA0B,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;YACtE,OAAO,CACL;gBACE,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE,qDAAqD;aAC5D,EACD,IAAI,CAAC,GAAG,EAAE,CACX,CAAC;QACJ,CAAC;QAED,OAAO;YACL,IAAI,EAAE,MAAM;YACZ,QAAQ,EAAE,YAAY,CAAC,QAAQ,CAAC,IAAI;YACpC,QAAQ,EAAE,YAAY,CAAC,QAAQ,CAAC,IAAI;SACrC,CAAC;IACJ,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO;YACL,IAAI,EAAE,SAAS;YACf,WAAW,EAAE,OAAO;YACpB,OAAO,EAAE,4DAA4D,KAAK,EAAE;SAC7E,CAAC;IACJ,CAAC;AACH,CAAC;AAED,KAAK,UAAU,UAAU,CACvB,OAAuB,EACvB,WAAmB;IAEnB,MAAM,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC;IAC7B,MAAM,EAAE,MAAM,EAAE,GAAG,QAAQ,CAAC;IAC5B,MAAM,aAAa,GAAG,MAAM,EAAE,iBAAiB,EAAE;QAC/C,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAAE,EAAE,aAAa,CAAC;QACtD,CAAC,CAAC,SAAS,CAAC;IACd,IAAI,CAAC,aAAa,EAAE,CAAC;QACnB,OAAO,EAAE,CAAC;IACZ,CAAC;IACD,IAAI,CAAC;QACH,MAAM,KAAK,GAAG,MAAM,EAAE,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;QAC9C,OAAO,KAAK;aACT,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;aACxC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;IAC9C,CAAC;IAAC,OAAO,IAAI,EAAE,CAAC;QACd,OAAO,EAAE,CAAC;IACZ,CAAC;AACH,CAAC;AAED,MAAM,CAAC,MAAM,cAAc,GAAG,CAAC,MAAqB,EAAuB,EAAE;IAC3E,IAAI,CAAC,MAAM,EAAE,uBAAuB,EAAE,EAAE,CAAC;QACvC,OAAO,IAAI,CAAC;IACd,CAAC;IAED,OAAO;QACL,IAAI,EAAE,SAAS;QACf,WAAW,EACT,gIAAgI;QAClI,IAAI,EAAE,WAAW,CAAC,QAAQ;QAC1B,MAAM,EAAE,aAAa;QACrB,UAAU;KACX,CAAC;AACJ,CAAC,CAAC"}