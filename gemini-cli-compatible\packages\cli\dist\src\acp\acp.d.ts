/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
import { Icon } from '@inkbytefo/s647-core';
import { WritableStream, ReadableStream } from 'node:stream/web';
export declare class ClientConnection implements Client {
    #private;
    constructor(agent: (client: Client) => Agent, input: WritableStream<Uint8Array>, output: ReadableStream<Uint8Array>);
    /**
     * Streams part of an assistant response to the client
     */
    streamAssistantMessageChunk(params: StreamAssistantMessageChunkParams): Promise<void>;
    /**
     * Request confirmation before running a tool
     *
     * When allowed, the client returns a [`ToolCallId`] which can be used
     * to update the tool call's `status` and `content` as it runs.
     */
    requestToolCallConfirmation(params: RequestToolCallConfirmationParams): Promise<RequestToolCallConfirmationResponse>;
    /**
     * pushToolCall allows the agent to start a tool call
     * when it does not need to request permission to do so.
     *
     * The returned id can be used to update the UI for the tool
     * call as needed.
     */
    pushToolCall(params: PushToolCallParams): Promise<PushToolCallResponse>;
    /**
     * updateToolCall allows the agent to update the content and status of the tool call.
     *
     * The new content replaces what is currently displayed in the UI.
     *
     * The [`ToolCallId`] is included in the response of
     * `pushToolCall` or `requestToolCallConfirmation` respectively.
     */
    updateToolCall(params: UpdateToolCallParams): Promise<void>;
}
type Result<T> = {
    result: T;
} | {
    error: ErrorResponse;
};
type ErrorResponse = {
    code: number;
    message: string;
    data?: {
        details?: string;
    };
};
export declare class RequestError extends Error {
    code: number;
    data?: {
        details?: string;
    };
    constructor(code: number, message: string, details?: string);
    static parseError(details?: string): RequestError;
    static invalidRequest(details?: string): RequestError;
    static methodNotFound(details?: string): RequestError;
    static invalidParams(details?: string): RequestError;
    static internalError(details?: string): RequestError;
    toResult<T>(): Result<T>;
}
export declare const LATEST_PROTOCOL_VERSION = "0.0.9";
export type AssistantMessageChunk = {
    text: string;
} | {
    thought: string;
};
export type ToolCallConfirmation = {
    description?: string | null;
    type: 'edit';
} | {
    description?: string | null;
    type: 'execute';
    command: string;
    rootCommand: string;
} | {
    description?: string | null;
    type: 'mcp';
    serverName: string;
    toolDisplayName: string;
    toolName: string;
} | {
    description?: string | null;
    type: 'fetch';
    urls: string[];
} | {
    description: string;
    type: 'other';
};
export type ToolCallContent = {
    type: 'markdown';
    markdown: string;
} | {
    type: 'diff';
    newText: string;
    oldText: string | null;
    path: string;
};
export type ToolCallStatus = 'running' | 'finished' | 'error';
export type ToolCallId = number;
export type ToolCallConfirmationOutcome = 'allow' | 'alwaysAllow' | 'alwaysAllowMcpServer' | 'alwaysAllowTool' | 'reject' | 'cancel';
/**
 * A part in a user message
 */
export type UserMessageChunk = {
    text: string;
} | {
    path: string;
};
export interface StreamAssistantMessageChunkParams {
    chunk: AssistantMessageChunk;
}
export interface RequestToolCallConfirmationParams {
    confirmation: ToolCallConfirmation;
    content?: ToolCallContent | null;
    icon: Icon;
    label: string;
    locations?: ToolCallLocation[];
}
export interface ToolCallLocation {
    line?: number | null;
    path: string;
}
export interface PushToolCallParams {
    content?: ToolCallContent | null;
    icon: Icon;
    label: string;
    locations?: ToolCallLocation[];
}
export interface UpdateToolCallParams {
    content: ToolCallContent | null;
    status: ToolCallStatus;
    toolCallId: ToolCallId;
}
export interface RequestToolCallConfirmationResponse {
    id: ToolCallId;
    outcome: ToolCallConfirmationOutcome;
}
export interface PushToolCallResponse {
    id: ToolCallId;
}
export interface InitializeParams {
    /**
     * The version of the protocol that the client supports.
     * This should be the latest version supported by the client.
     */
    protocolVersion: string;
}
export interface SendUserMessageParams {
    chunks: UserMessageChunk[];
}
export interface InitializeResponse {
    /**
     * Indicates whether the agent is authenticated and
     * ready to handle requests.
     */
    isAuthenticated: boolean;
    /**
     * The version of the protocol that the agent supports.
     * If the agent supports the requested version, it should respond with the same version.
     * Otherwise, the agent should respond with the latest version it supports.
     */
    protocolVersion: string;
}
export interface Error {
    code: number;
    data?: unknown;
    message: string;
}
export interface Client {
    streamAssistantMessageChunk(params: StreamAssistantMessageChunkParams): Promise<void>;
    requestToolCallConfirmation(params: RequestToolCallConfirmationParams): Promise<RequestToolCallConfirmationResponse>;
    pushToolCall(params: PushToolCallParams): Promise<PushToolCallResponse>;
    updateToolCall(params: UpdateToolCallParams): Promise<void>;
}
export interface Agent {
    /**
     * Initializes the agent's state. It should be called before any other method,
     * and no other methods should be called until it has completed.
     *
     * If the agent is not authenticated, then the client should prompt the user to authenticate,
     * and then call the `authenticate` method.
     * Otherwise the client can send other messages to the agent.
     */
    initialize(params: InitializeParams): Promise<InitializeResponse>;
    /**
     * Begins the authentication process.
     *
     * This method should only be called if `initialize` indicates the user isn't already authenticated.
     * The Promise MUST not resolve until authentication is complete.
     */
    authenticate(): Promise<void>;
    /**
     * Allows the user to send a message to the agent.
     * This method should complete after the agent is finished, during
     * which time the agent may update the client by calling
     * streamAssistantMessageChunk and other methods.
     */
    sendUserMessage(params: SendUserMessageParams): Promise<void>;
    /**
     * Cancels the current generation.
     */
    cancelSendMessage(): Promise<void>;
}
export {};
