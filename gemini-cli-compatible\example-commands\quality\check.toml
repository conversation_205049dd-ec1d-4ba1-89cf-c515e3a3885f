# Quality Check Command
# Usage: /quality:check
# This command runs comprehensive quality checks on the current codebase

description = "Run comprehensive quality checks including tests, linting, type checking, and build verification."

prompt = """
Please perform a comprehensive quality check on the current codebase.

Execute the following quality assurance steps:

## 1. Build Verification
- Run the project's build command to ensure compilation succeeds
- Check for any build warnings or errors
- Verify all dependencies are properly resolved

## 2. Test Execution
- Run the full test suite to ensure all tests pass
- Check test coverage and identify any gaps
- Run specific tests related to recent changes

## 3. Code Quality Checks
- Execute linting tools to check code style and conventions
- Run type checking (if applicable) to catch type errors
- Check for security vulnerabilities in dependencies

## 4. Standards Compliance
- Verify adherence to project coding standards
- Check documentation completeness
- Validate commit message format (if in git repository)

## 5. Performance Checks
- Identify any performance regressions
- Check bundle size (for web projects)
- Validate memory usage patterns

Please report:
- ✅ All checks that pass
- ❌ Any issues found with specific details
- 🔧 Recommended fixes for any problems
- 📊 Summary of overall code health

If any critical issues are found, provide step-by-step instructions to resolve them.
"""
