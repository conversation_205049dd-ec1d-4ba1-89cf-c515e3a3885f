{"version": 3, "file": "useHistoryManager.js", "sourceRoot": "", "sources": ["../../../../src/ui/hooks/useHistoryManager.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,OAAO,CAAC;AAmBtD;;;;;GAKG;AACH,MAAM,UAAU,UAAU;IACxB,MAAM,CAAC,OAAO,EAAE,UAAU,CAAC,GAAG,QAAQ,CAAgB,EAAE,CAAC,CAAC;IAC1D,MAAM,mBAAmB,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IAEtC,oEAAoE;IACpE,MAAM,gBAAgB,GAAG,WAAW,CAAC,CAAC,aAAqB,EAAU,EAAE;QACrE,mBAAmB,CAAC,OAAO,IAAI,CAAC,CAAC;QACjC,OAAO,aAAa,GAAG,mBAAmB,CAAC,OAAO,CAAC;IACrD,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,MAAM,WAAW,GAAG,WAAW,CAAC,CAAC,UAAyB,EAAE,EAAE;QAC5D,UAAU,CAAC,UAAU,CAAC,CAAC;IACzB,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,yDAAyD;IACzD,MAAM,OAAO,GAAG,WAAW,CACzB,CAAC,QAAiC,EAAE,aAAqB,EAAU,EAAE;QACnE,MAAM,EAAE,GAAG,gBAAgB,CAAC,aAAa,CAAC,CAAC;QAC3C,MAAM,OAAO,GAAgB,EAAE,GAAG,QAAQ,EAAE,EAAE,EAAiB,CAAC;QAEhE,UAAU,CAAC,CAAC,WAAW,EAAE,EAAE;YACzB,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC3B,MAAM,QAAQ,GAAG,WAAW,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBACrD,qDAAqD;gBACrD,IACE,QAAQ,CAAC,IAAI,KAAK,MAAM;oBACxB,OAAO,CAAC,IAAI,KAAK,MAAM;oBACvB,QAAQ,CAAC,IAAI,KAAK,OAAO,CAAC,IAAI,EAC9B,CAAC;oBACD,OAAO,WAAW,CAAC,CAAC,0BAA0B;gBAChD,CAAC;YACH,CAAC;YACD,OAAO,CAAC,GAAG,WAAW,EAAE,OAAO,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC;QACH,OAAO,EAAE,CAAC,CAAC,iEAAiE;IAC9E,CAAC,EACD,CAAC,gBAAgB,CAAC,CACnB,CAAC;IAEF;;;;;OAKG;IACH,EAAE;IACF,MAAM,UAAU,GAAG,WAAW,CAC5B,CACE,EAAU,EACV,OAA8D,EAC9D,EAAE;QACF,UAAU,CAAC,CAAC,WAAW,EAAE,EAAE,CACzB,WAAW,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;YACvB,IAAI,IAAI,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC;gBACnB,8DAA8D;gBAC9D,MAAM,UAAU,GACd,OAAO,OAAO,KAAK,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;gBAC1D,OAAO,EAAE,GAAG,IAAI,EAAE,GAAG,UAAU,EAAiB,CAAC;YACnD,CAAC;YACD,OAAO,IAAI,CAAC;QACd,CAAC,CAAC,CACH,CAAC;IACJ,CAAC,EACD,EAAE,CACH,CAAC;IAEF,6DAA6D;IAC7D,MAAM,UAAU,GAAG,WAAW,CAAC,GAAG,EAAE;QAClC,UAAU,CAAC,EAAE,CAAC,CAAC;QACf,mBAAmB,CAAC,OAAO,GAAG,CAAC,CAAC;IAClC,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,OAAO;QACL,OAAO;QACP,OAAO;QACP,UAAU;QACV,UAAU;QACV,WAAW;KACZ,CAAC;AACJ,CAAC"}