{"version": 3, "file": "copyCommand.js", "sourceRoot": "", "sources": ["../../../../src/ui/commands/copyCommand.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,eAAe,EAAE,MAAM,0BAA0B,CAAC;AAC3D,OAAO,EACL,WAAW,GAGZ,MAAM,YAAY,CAAC;AAEpB,MAAM,CAAC,MAAM,WAAW,GAAiB;IACvC,IAAI,EAAE,MAAM;IACZ,WAAW,EAAE,mDAAmD;IAChE,IAAI,EAAE,WAAW,CAAC,QAAQ;IAC1B,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAA4C,EAAE;QACzE,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,QAAQ,CAAC,MAAM,EAAE,eAAe,EAAE,EAAE,OAAO,EAAE,CAAC;QACzE,MAAM,OAAO,GAAG,IAAI,EAAE,UAAU,EAAE,CAAC;QAEnC,gDAAgD;QAChD,MAAM,aAAa,GAAG,OAAO;YAC3B,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,OAAO,CAAC,CAAC,GAAG,EAAE;YACvD,CAAC,CAAC,SAAS,CAAC;QAEd,IAAI,CAAC,aAAa,EAAE,CAAC;YACnB,OAAO;gBACL,IAAI,EAAE,SAAS;gBACf,WAAW,EAAE,MAAM;gBACnB,OAAO,EAAE,sBAAsB;aAChC,CAAC;QACJ,CAAC;QACD,8BAA8B;QAC9B,MAAM,YAAY,GAAG,aAAa,CAAC,KAAK;YACtC,EAAE,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC;aAC5B,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC;aACxB,IAAI,CAAC,EAAE,CAAC,CAAC;QAEZ,IAAI,YAAY,EAAE,CAAC;YACjB,IAAI,CAAC;gBACH,MAAM,eAAe,CAAC,YAAY,CAAC,CAAC;gBAEpC,OAAO;oBACL,IAAI,EAAE,SAAS;oBACf,WAAW,EAAE,MAAM;oBACnB,OAAO,EAAE,qCAAqC;iBAC/C,CAAC;YACJ,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,OAAO,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBACvE,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;gBAEvB,OAAO;oBACL,IAAI,EAAE,SAAS;oBACf,WAAW,EAAE,OAAO;oBACpB,OAAO,EAAE,kCAAkC;iBAC5C,CAAC;YACJ,CAAC;QACH,CAAC;aAAM,CAAC;YACN,OAAO;gBACL,IAAI,EAAE,SAAS;gBACf,WAAW,EAAE,MAAM;gBACnB,OAAO,EAAE,0CAA0C;aACpD,CAAC;QACJ,CAAC;IACH,CAAC;CACF,CAAC"}