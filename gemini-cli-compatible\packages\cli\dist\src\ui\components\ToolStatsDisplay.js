import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Box, Text } from 'ink';
import { Colors } from '../colors.js';
import { formatDuration } from '../utils/formatters.js';
import { getStatusColor, TOOL_SUCCESS_RATE_HIGH, TOOL_SUCCESS_RATE_MEDIUM, USER_AGREEMENT_RATE_HIGH, USER_AGREEMENT_RATE_MEDIUM, } from '../utils/displayUtils.js';
import { useSessionStats } from '../contexts/SessionContext.js';
const TOOL_NAME_COL_WIDTH = 25;
const CALLS_COL_WIDTH = 8;
const SUCCESS_RATE_COL_WIDTH = 15;
const AVG_DURATION_COL_WIDTH = 15;
const StatRow = ({ name, stats }) => {
    const successRate = stats.count > 0 ? (stats.success / stats.count) * 100 : 0;
    const avgDuration = stats.count > 0 ? stats.durationMs / stats.count : 0;
    const successColor = getStatusColor(successRate, {
        green: TOOL_SUCCESS_RATE_HIGH,
        yellow: TOOL_SUCCESS_RATE_MEDIUM,
    });
    return (_jsxs(Box, { children: [_jsx(Box, { width: TOOL_NAME_COL_WIDTH, children: _jsx(Text, { color: Colors.LightBlue, children: name }) }), _jsx(Box, { width: CALLS_COL_WIDTH, justifyContent: "flex-end", children: _jsx(Text, { children: stats.count }) }), _jsx(Box, { width: SUCCESS_RATE_COL_WIDTH, justifyContent: "flex-end", children: _jsxs(Text, { color: successColor, children: [successRate.toFixed(1), "%"] }) }), _jsx(Box, { width: AVG_DURATION_COL_WIDTH, justifyContent: "flex-end", children: _jsx(Text, { children: formatDuration(avgDuration) }) })] }));
};
export const ToolStatsDisplay = () => {
    const { stats } = useSessionStats();
    const { tools } = stats.metrics;
    const activeTools = Object.entries(tools.byName).filter(([, metrics]) => metrics.count > 0);
    if (activeTools.length === 0) {
        return (_jsx(Box, { borderStyle: "round", borderColor: Colors.Gray, paddingY: 1, paddingX: 2, children: _jsx(Text, { children: "No tool calls have been made in this session." }) }));
    }
    const totalDecisions = Object.values(tools.byName).reduce((acc, tool) => {
        acc.accept += tool.decisions.accept;
        acc.reject += tool.decisions.reject;
        acc.modify += tool.decisions.modify;
        return acc;
    }, { accept: 0, reject: 0, modify: 0 });
    const totalReviewed = totalDecisions.accept + totalDecisions.reject + totalDecisions.modify;
    const agreementRate = totalReviewed > 0 ? (totalDecisions.accept / totalReviewed) * 100 : 0;
    const agreementColor = getStatusColor(agreementRate, {
        green: USER_AGREEMENT_RATE_HIGH,
        yellow: USER_AGREEMENT_RATE_MEDIUM,
    });
    return (_jsxs(Box, { borderStyle: "round", borderColor: Colors.Gray, flexDirection: "column", paddingY: 1, paddingX: 2, width: 70, children: [_jsx(Text, { bold: true, color: Colors.AccentPurple, children: "Tool Stats For Nerds" }), _jsx(Box, { height: 1 }), _jsxs(Box, { children: [_jsx(Box, { width: TOOL_NAME_COL_WIDTH, children: _jsx(Text, { bold: true, children: "Tool Name" }) }), _jsx(Box, { width: CALLS_COL_WIDTH, justifyContent: "flex-end", children: _jsx(Text, { bold: true, children: "Calls" }) }), _jsx(Box, { width: SUCCESS_RATE_COL_WIDTH, justifyContent: "flex-end", children: _jsx(Text, { bold: true, children: "Success Rate" }) }), _jsx(Box, { width: AVG_DURATION_COL_WIDTH, justifyContent: "flex-end", children: _jsx(Text, { bold: true, children: "Avg Duration" }) })] }), _jsx(Box, { borderStyle: "single", borderBottom: true, borderTop: false, borderLeft: false, borderRight: false, width: "100%" }), activeTools.map(([name, stats]) => (_jsx(StatRow, { name: name, stats: stats }, name))), _jsx(Box, { height: 1 }), _jsx(Text, { bold: true, children: "User Decision Summary" }), _jsxs(Box, { children: [_jsx(Box, { width: TOOL_NAME_COL_WIDTH + CALLS_COL_WIDTH + SUCCESS_RATE_COL_WIDTH, children: _jsx(Text, { color: Colors.LightBlue, children: "Total Reviewed Suggestions:" }) }), _jsx(Box, { width: AVG_DURATION_COL_WIDTH, justifyContent: "flex-end", children: _jsx(Text, { children: totalReviewed }) })] }), _jsxs(Box, { children: [_jsx(Box, { width: TOOL_NAME_COL_WIDTH + CALLS_COL_WIDTH + SUCCESS_RATE_COL_WIDTH, children: _jsx(Text, { children: " \u00BB Accepted:" }) }), _jsx(Box, { width: AVG_DURATION_COL_WIDTH, justifyContent: "flex-end", children: _jsx(Text, { color: Colors.AccentGreen, children: totalDecisions.accept }) })] }), _jsxs(Box, { children: [_jsx(Box, { width: TOOL_NAME_COL_WIDTH + CALLS_COL_WIDTH + SUCCESS_RATE_COL_WIDTH, children: _jsx(Text, { children: " \u00BB Rejected:" }) }), _jsx(Box, { width: AVG_DURATION_COL_WIDTH, justifyContent: "flex-end", children: _jsx(Text, { color: Colors.AccentRed, children: totalDecisions.reject }) })] }), _jsxs(Box, { children: [_jsx(Box, { width: TOOL_NAME_COL_WIDTH + CALLS_COL_WIDTH + SUCCESS_RATE_COL_WIDTH, children: _jsx(Text, { children: " \u00BB Modified:" }) }), _jsx(Box, { width: AVG_DURATION_COL_WIDTH, justifyContent: "flex-end", children: _jsx(Text, { color: Colors.AccentYellow, children: totalDecisions.modify }) })] }), _jsx(Box, { borderStyle: "single", borderBottom: true, borderTop: false, borderLeft: false, borderRight: false, width: "100%" }), _jsxs(Box, { children: [_jsx(Box, { width: TOOL_NAME_COL_WIDTH + CALLS_COL_WIDTH + SUCCESS_RATE_COL_WIDTH, children: _jsx(Text, { children: " Overall Agreement Rate:" }) }), _jsx(Box, { width: AVG_DURATION_COL_WIDTH, justifyContent: "flex-end", children: _jsx(Text, { bold: true, color: totalReviewed > 0 ? agreementColor : undefined, children: totalReviewed > 0 ? `${agreementRate.toFixed(1)}%` : '--' }) })] })] }));
};
//# sourceMappingURL=ToolStatsDisplay.js.map