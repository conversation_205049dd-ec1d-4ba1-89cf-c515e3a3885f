{"version": "3.2.4", "results": [[":src/ui/components/shared/text-buffer.test.ts", {"duration": 526.6634999999997, "failed": false}], [":src/ui/hooks/useGeminiStream.test.tsx", {"duration": 701.4417000000003, "failed": false}], [":src/ui/hooks/atCommandProcessor.test.ts", {"duration": 104.1243000000004, "failed": true}], [":src/config/config.test.ts", {"duration": 927.9227999999994, "failed": false}], [":src/config/settings.test.ts", {"duration": 84.32130000000052, "failed": false}], [":src/ui/commands/mcpCommand.test.ts", {"duration": 130.11030000000028, "failed": false}], [":src/ui/components/InputPrompt.test.tsx", {"duration": 4494.914599999999, "failed": true}], [":src/ui/hooks/useToolScheduler.test.ts", {"duration": 105.91539999999986, "failed": false}], [":src/ui/hooks/useCompletion.test.ts", {"duration": 2306.978100000001, "failed": false}], [":src/ui/hooks/useCompletion.integration.test.ts", {"duration": 1860.4609999999993, "failed": true}], [":src/ui/App.test.tsx", {"duration": 889.6380000000008, "failed": false}], [":src/ui/hooks/slashCommandProcessor.test.ts", {"duration": 981.9384, "failed": false}], [":src/ui/utils/errorParsing.test.ts", {"duration": 14.639799999999923, "failed": false}], [":src/ui/components/shared/MaxSizedBox.test.tsx", {"duration": 250.538, "failed": false}], [":src/nonInteractiveCli.test.ts", {"duration": 25.352400000000216, "failed": false}], [":src/ui/components/messages/DiffRenderer.test.tsx", {"duration": 602.6833000000001, "failed": false}], [":src/ui/utils/commandUtils.test.ts", {"duration": 99.36329999999998, "failed": false}], [":src/ui/components/AuthDialog.test.tsx", {"duration": 684.1552000000001, "failed": true}], [":src/ui/hooks/useAutoAcceptIndicator.test.ts", {"duration": 76.16740000000027, "failed": false}], [":src/ui/components/StatsDisplay.test.tsx", {"duration": 363.6360000000004, "failed": true}], [":src/ui/commands/chatCommand.test.ts", {"duration": 30.17320000000018, "failed": false}], [":src/ui/commands/ideCommand.test.ts", {"duration": 45.79710000000023, "failed": true}], [":src/ui/themes/color-utils.test.ts", {"duration": 17.92390000000023, "failed": false}], [":src/ui/commands/restoreCommand.test.ts", {"duration": 52.448599999999715, "failed": true}], [":src/ui/commands/copyCommand.test.ts", {"duration": 24.247600000000148, "failed": false}], [":src/ui/hooks/useEditorSettings.test.ts", {"duration": 104.71099999999933, "failed": false}], [":src/config/config.integration.test.ts", {"duration": 161.9975999999997, "failed": false}], [":src/ui/hooks/useInputHistory.test.ts", {"duration": 65.27239999999983, "failed": false}], [":src/ui/hooks/useKeypress.test.ts", {"duration": 165.91859999999997, "failed": false}], [":src/ui/commands/memoryCommand.test.ts", {"duration": 21.883700000000317, "failed": false}], [":src/ui/hooks/useGitBranchName.test.ts", {"duration": 62.86359999999968, "failed": false}], [":src/services/FileCommandLoader.test.ts", {"duration": 84.1238999999996, "failed": false}], [":src/ui/components/LoadingIndicator.test.tsx", {"duration": 86.70979999999963, "failed": false}], [":src/ui/hooks/useShellHistory.test.ts", {"duration": 834.9493000000002, "failed": false}], [":src/ui/hooks/useHistoryManager.test.ts", {"duration": 69.47659999999996, "failed": false}], [":src/ui/components/ModelStatsDisplay.test.tsx", {"duration": 379.34339999999975, "failed": true}], [":src/ui/components/messages/ToolMessage.test.tsx", {"duration": 142.7741000000001, "failed": false}], [":src/ui/utils/computeStats.test.ts", {"duration": 9.9983000000002, "failed": false}], [":src/services/CommandService.test.ts", {"duration": 26.114400000000387, "failed": false}], [":src/validateNonInterActiveAuth.test.ts", {"duration": 36.80550000000039, "failed": true}], [":src/ui/hooks/useConsoleMessages.test.ts", {"duration": 67.90329999999994, "failed": false}], [":src/ui/hooks/shellCommandProcessor.test.ts", {"duration": 65.80289999999968, "failed": true}], [":src/ui/hooks/usePhraseCycler.test.ts", {"duration": 79.65300000000025, "failed": false}], [":src/config/extension.test.ts", {"duration": 31.529799999999796, "failed": false}], [":src/ui/components/ToolStatsDisplay.test.tsx", {"duration": 184.13760000000002, "failed": false}], [":src/services/BuiltinCommandLoader.test.ts", {"duration": 11.580699999999979, "failed": false}], [":src/gemini.test.tsx", {"duration": 13.541900000000169, "failed": false}], [":src/ui/hooks/useLoadingIndicator.test.ts", {"duration": 65.36149999999998, "failed": false}], [":src/ui/utils/MarkdownDisplay.test.tsx", {"duration": 519.2321999999999, "failed": false}], [":src/utils/userStartupWarnings.test.ts", {"duration": 48.73169999999982, "failed": true}], [":src/ui/commands/compressCommand.test.ts", {"duration": 20.569099999999708, "failed": false}], [":src/ui/hooks/useTimer.test.ts", {"duration": 217.66809999999987, "failed": false}], [":src/ui/themes/theme-manager.test.ts", {"duration": 31.093700000000354, "failed": false}], [":src/ui/contexts/SessionContext.test.tsx", {"duration": 111.98930000000018, "failed": false}], [":src/ui/commands/aboutCommand.test.ts", {"duration": 17.733400000000074, "failed": false}], [":src/ui/commands/toolsCommand.test.ts", {"duration": 17.035199999999804, "failed": false}], [":src/ui/hooks/useFocus.test.ts", {"duration": 49.73090000000002, "failed": false}], [":src/ui/commands/clearCommand.test.ts", {"duration": 11.95049999999992, "failed": false}], [":src/ui/components/shared/RadioButtonSelect.test.tsx", {"duration": 169.95870000000014, "failed": false}], [":src/ui/components/HistoryItemDisplay.test.tsx", {"duration": 150.21759999999995, "failed": false}], [":src/ui/commands/bugCommand.test.ts", {"duration": 17.63000000000011, "failed": false}], [":src/ui/utils/updateCheck.test.ts", {"duration": 12.633600000000115, "failed": false}], [":src/ui/commands/docsCommand.test.ts", {"duration": 16.41339999999991, "failed": false}], [":src/config/auth.test.ts", {"duration": 35.831200000000536, "failed": true}], [":src/utils/startupWarnings.test.ts", {"duration": 0, "failed": false}], [":src/ui/commands/statsCommand.test.ts", {"duration": 17.704999999999927, "failed": false}], [":src/ui/utils/clipboardUtils.test.ts", {"duration": 9.057800000000043, "failed": false}], [":src/ui/utils/markdownUtilities.test.ts", {"duration": 6.541899999999714, "failed": false}], [":src/ui/utils/formatters.test.ts", {"duration": 8.19459999999981, "failed": false}], [":src/ui/commands/extensionsCommand.test.ts", {"duration": 13.954600000000028, "failed": false}], [":src/ui/components/SessionSummaryDisplay.test.tsx", {"duration": 103.9375, "failed": true}], [":src/test-utils/mockCommandContext.test.ts", {"duration": 11.615999999999985, "failed": false}], [":src/ui/utils/displayUtils.test.ts", {"duration": 7.058899999999994, "failed": false}], [":src/ui/components/messages/ToolConfirmationMessage.test.tsx", {"duration": 63.107199999999466, "failed": false}], [":src/ui/commands/quitCommand.test.ts", {"duration": 12.461199999999735, "failed": false}], [":src/ui/commands/helpCommand.test.ts", {"duration": 10.68979999999965, "failed": false}], [":src/ui/utils/textUtils.test.ts", {"duration": 6.565700000000106, "failed": false}], [":src/ui/commands/privacyCommand.test.ts", {"duration": 9.312800000000152, "failed": false}], [":src/ui/commands/themeCommand.test.ts", {"duration": 10.365099999999984, "failed": false}], [":src/ui/commands/corgiCommand.test.ts", {"duration": 9.528999999999996, "failed": false}], [":src/ui/commands/authCommand.test.ts", {"duration": 8.031399999999849, "failed": false}], [":src/ui/commands/editorCommand.test.ts", {"duration": 8.333599999999933, "failed": false}]]}