{"version": 3, "file": "bugCommand.js", "sourceRoot": "", "sources": ["../../../../src/ui/commands/bugCommand.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,IAAI,MAAM,MAAM,CAAC;AACxB,OAAO,OAAO,MAAM,cAAc,CAAC;AACnC,OAAO,EAGL,WAAW,GACZ,MAAM,YAAY,CAAC;AACpB,OAAO,EAAE,WAAW,EAAE,MAAM,aAAa,CAAC;AAC1C,OAAO,EAAE,eAAe,EAAE,MAAM,+BAA+B,CAAC;AAChE,OAAO,EAAE,iBAAiB,EAAE,MAAM,wBAAwB,CAAC;AAC3D,OAAO,EAAE,aAAa,EAAE,MAAM,wBAAwB,CAAC;AAEvD,MAAM,CAAC,MAAM,UAAU,GAAiB;IACtC,IAAI,EAAE,KAAK;IACX,WAAW,EAAE,qBAAqB;IAClC,IAAI,EAAE,WAAW,CAAC,QAAQ;IAC1B,MAAM,EAAE,KAAK,EAAE,OAAuB,EAAE,IAAa,EAAiB,EAAE;QACtE,MAAM,cAAc,GAAG,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;QAC3C,MAAM,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC,QAAQ,CAAC;QAEpC,MAAM,SAAS,GAAG,GAAG,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;QAC3D,IAAI,UAAU,GAAG,YAAY,CAAC;QAC9B,IAAI,OAAO,CAAC,GAAG,CAAC,OAAO,IAAI,OAAO,CAAC,GAAG,CAAC,OAAO,KAAK,cAAc,EAAE,CAAC;YAClE,UAAU,GAAG,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,oBAAoB,EAAE,EAAE,CAAC,CAAC;QACrE,CAAC;aAAM,IAAI,OAAO,CAAC,GAAG,CAAC,OAAO,KAAK,cAAc,EAAE,CAAC;YAClD,UAAU,GAAG,iBACX,OAAO,CAAC,GAAG,CAAC,gBAAgB,IAAI,SAClC,GAAG,CAAC;QACN,CAAC;QACD,MAAM,YAAY,GAAG,MAAM,EAAE,QAAQ,EAAE,IAAI,SAAS,CAAC;QACrD,MAAM,UAAU,GAAG,MAAM,aAAa,EAAE,CAAC;QACzC,MAAM,WAAW,GAAG,iBAAiB,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,GAAG,CAAC,CAAC;QAEjE,MAAM,IAAI,GAAG;qBACI,UAAU;oBACX,eAAe;0BACT,SAAS;6BACN,UAAU;uBAChB,YAAY;sBACb,WAAW;CAChC,CAAC;QAEE,IAAI,YAAY,GACd,0GAA0G,CAAC;QAE7G,MAAM,kBAAkB,GAAG,MAAM,EAAE,aAAa,EAAE,CAAC;QACnD,IAAI,kBAAkB,EAAE,WAAW,EAAE,CAAC;YACpC,YAAY,GAAG,kBAAkB,CAAC,WAAW,CAAC;QAChD,CAAC;QAED,YAAY,GAAG,YAAY;aACxB,OAAO,CAAC,SAAS,EAAE,kBAAkB,CAAC,cAAc,CAAC,CAAC;aACtD,OAAO,CAAC,QAAQ,EAAE,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC;QAE/C,OAAO,CAAC,EAAE,CAAC,OAAO,CAChB;YACE,IAAI,EAAE,WAAW,CAAC,IAAI;YACtB,IAAI,EAAE,8EAA8E,YAAY,EAAE;SACnG,EACD,IAAI,CAAC,GAAG,EAAE,CACX,CAAC;QAEF,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,YAAY,CAAC,CAAC;QAC3B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAChB,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACzD,OAAO,CAAC,EAAE,CAAC,OAAO,CAChB;gBACE,IAAI,EAAE,WAAW,CAAC,KAAK;gBACvB,IAAI,EAAE,kCAAkC,YAAY,EAAE;aACvD,EACD,IAAI,CAAC,GAAG,EAAE,CACX,CAAC;QACJ,CAAC;IACH,CAAC;CACF,CAAC"}