/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
import React from 'react';
import { type OpenFiles, type MCPServerConfig } from '@inkbytefo/s647-core';
interface ContextSummaryDisplayProps {
    geminiMdFileCount: number;
    contextFileNames: string[];
    mcpServers?: Record<string, MCPServerConfig>;
    blockedMcpServers?: Array<{
        name: string;
        extensionName: string;
    }>;
    showToolDescriptions?: boolean;
    openFiles?: OpenFiles;
}
export declare const ContextSummaryDisplay: React.FC<ContextSummaryDisplayProps>;
export {};
