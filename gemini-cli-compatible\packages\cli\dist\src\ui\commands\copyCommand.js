/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
import { copyToClipboard } from '../utils/commandUtils.js';
import { CommandKind, } from './types.js';
export const copyCommand = {
    name: 'copy',
    description: 'Copy the last result or code snippet to clipboard',
    kind: CommandKind.BUILT_IN,
    action: async (context, _args) => {
        const chat = await context.services.config?.getGeminiClient()?.getChat();
        const history = chat?.getHistory();
        // Get the last message from the AI (model role)
        const lastAiMessage = history
            ? history.filter((item) => item.role === 'model').pop()
            : undefined;
        if (!lastAiMessage) {
            return {
                type: 'message',
                messageType: 'info',
                content: 'No output in history',
            };
        }
        // Extract text from the parts
        const lastAiOutput = lastAiMessage.parts
            ?.filter((part) => part.text)
            .map((part) => part.text)
            .join('');
        if (lastAiOutput) {
            try {
                await copyToClipboard(lastAiOutput);
                return {
                    type: 'message',
                    messageType: 'info',
                    content: 'Last output copied to the clipboard',
                };
            }
            catch (error) {
                const message = error instanceof Error ? error.message : String(error);
                console.debug(message);
                return {
                    type: 'message',
                    messageType: 'error',
                    content: 'Failed to copy to the clipboard.',
                };
            }
        }
        else {
            return {
                type: 'message',
                messageType: 'info',
                content: 'Last AI output contains no text to copy.',
            };
        }
    },
};
//# sourceMappingURL=copyCommand.js.map