{"version": 3, "file": "extensionsCommand.js", "sourceRoot": "", "sources": ["../../../../src/ui/commands/extensionsCommand.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAGL,WAAW,GACZ,MAAM,YAAY,CAAC;AACpB,OAAO,EAAE,WAAW,EAAE,MAAM,aAAa,CAAC;AAE1C,MAAM,CAAC,MAAM,iBAAiB,GAAiB;IAC7C,IAAI,EAAE,YAAY;IAClB,WAAW,EAAE,wBAAwB;IACrC,IAAI,EAAE,WAAW,CAAC,QAAQ;IAC1B,MAAM,EAAE,KAAK,EAAE,OAAuB,EAAiB,EAAE;QACvD,MAAM,gBAAgB,GAAG,OAAO,CAAC,QAAQ,CAAC,MAAM;YAC9C,EAAE,aAAa,EAAE;aAChB,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACjC,IAAI,CAAC,gBAAgB,IAAI,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACvD,OAAO,CAAC,EAAE,CAAC,OAAO,CAChB;gBACE,IAAI,EAAE,WAAW,CAAC,IAAI;gBACtB,IAAI,EAAE,uBAAuB;aAC9B,EACD,IAAI,CAAC,GAAG,EAAE,CACX,CAAC;YACF,OAAO;QACT,CAAC;QAED,MAAM,cAAc,GAAG,gBAAgB,CAAC,GAAG,CACzC,CAAC,GAAG,EAAE,EAAE,CAAC,iBAAiB,GAAG,CAAC,IAAI,MAAM,GAAG,CAAC,OAAO,YAAY,CAChE,CAAC;QACF,MAAM,OAAO,GAAG,yBAAyB,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;QAEvE,OAAO,CAAC,EAAE,CAAC,OAAO,CAChB;YACE,IAAI,EAAE,WAAW,CAAC,IAAI;YACtB,IAAI,EAAE,OAAO;SACd,EACD,IAAI,CAAC,GAAG,EAAE,CACX,CAAC;IACJ,CAAC;CACF,CAAC"}