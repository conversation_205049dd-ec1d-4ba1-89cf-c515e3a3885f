# Task Planning Command
# Usage: /task:plan
# This command helps break down complex work into manageable tasks

description = "Analyze the current request and create a structured task plan with clear steps and verification points."

prompt = """
Please analyze the current context and user request to create a comprehensive task plan.

Your response should include:

1. **Task Breakdown**: Break the work into meaningful subtasks (approximately 20 minutes each)
2. **Dependencies**: Identify prerequisites and dependencies between tasks
3. **Verification Strategy**: Plan how to test and verify each step
4. **Risk Assessment**: Identify potential challenges or blockers
5. **Timeline Estimate**: Provide rough time estimates for each task

Format your response as a structured plan with:
- Clear task descriptions
- Dependency relationships
- Verification checkpoints
- Success criteria for each task

Consider the project's existing conventions, testing frameworks, and quality standards when creating the plan.
"""
