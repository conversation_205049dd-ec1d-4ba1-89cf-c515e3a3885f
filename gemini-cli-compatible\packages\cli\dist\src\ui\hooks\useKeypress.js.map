{"version": 3, "file": "useKeypress.js", "sourceRoot": "", "sources": ["../../../../src/ui/hooks/useKeypress.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,SAAS,EAAE,MAAM,EAAE,MAAM,OAAO,CAAC;AAC1C,OAAO,EAAE,QAAQ,EAAE,MAAM,KAAK,CAAC;AAC/B,OAAO,QAAQ,MAAM,UAAU,CAAC;AAChC,OAAO,EAAE,WAAW,EAAE,MAAM,QAAQ,CAAC;AAWrC;;;;;;;;;;;;GAYG;AACH,MAAM,UAAU,WAAW,CACzB,UAA8B,EAC9B,EAAE,QAAQ,EAAyB;IAEnC,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG,QAAQ,EAAE,CAAC;IACzC,MAAM,aAAa,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC;IAEzC,SAAS,CAAC,GAAG,EAAE;QACb,aAAa,CAAC,OAAO,GAAG,UAAU,CAAC;IACrC,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC;IAEjB,SAAS,CAAC,GAAG,EAAE;QACb,IAAI,CAAC,QAAQ,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;YAC9B,OAAO;QACT,CAAC;QAED,UAAU,CAAC,IAAI,CAAC,CAAC;QAEjB,MAAM,cAAc,GAAG,IAAI,WAAW,EAAE,CAAC;QACzC,IAAI,cAAc,GAAG,KAAK,CAAC;QAC3B,MAAM,gBAAgB,GAAG,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAC3E,IACE,gBAAgB,GAAG,EAAE;YACrB,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,KAAK,GAAG;YACvC,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,KAAK,MAAM,EAC1C,CAAC;YACD,wEAAwE;YACxE,4DAA4D;YAC5D,cAAc,GAAG,IAAI,CAAC;QACxB,CAAC;QAED,IAAI,OAAO,GAAG,KAAK,CAAC;QACpB,IAAI,WAAW,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAElC,MAAM,cAAc,GAAG,CAAC,CAAU,EAAE,GAAQ,EAAE,EAAE;YAC9C,IAAI,GAAG,CAAC,IAAI,KAAK,aAAa,EAAE,CAAC;gBAC/B,OAAO,GAAG,IAAI,CAAC;YACjB,CAAC;iBAAM,IAAI,GAAG,CAAC,IAAI,KAAK,WAAW,EAAE,CAAC;gBACpC,OAAO,GAAG,KAAK,CAAC;gBAChB,aAAa,CAAC,OAAO,CAAC;oBACpB,IAAI,EAAE,EAAE;oBACR,IAAI,EAAE,KAAK;oBACX,IAAI,EAAE,KAAK;oBACX,KAAK,EAAE,KAAK;oBACZ,KAAK,EAAE,IAAI;oBACX,QAAQ,EAAE,WAAW,CAAC,QAAQ,EAAE;iBACjC,CAAC,CAAC;gBACH,WAAW,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAChC,CAAC;iBAAM,CAAC;gBACN,IAAI,OAAO,EAAE,CAAC;oBACZ,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;gBACxE,CAAC;qBAAM,CAAC;oBACN,sBAAsB;oBACtB,IAAI,GAAG,CAAC,IAAI,KAAK,QAAQ,IAAI,GAAG,CAAC,QAAQ,KAAK,QAAQ,EAAE,CAAC;wBACvD,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC;oBAClB,CAAC;oBACD,aAAa,CAAC,OAAO,CAAC,EAAE,GAAG,GAAG,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC,CAAC;gBACpD,CAAC;YACH,CAAC;QACH,CAAC,CAAC;QAEF,MAAM,iBAAiB,GAAG,CAAC,IAAY,EAAE,EAAE;YACzC,MAAM,iBAAiB,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACnD,MAAM,iBAAiB,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAEnD,IAAI,GAAG,GAAG,CAAC,CAAC;YACZ,OAAO,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;gBACzB,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,GAAG,CAAC,CAAC;gBACvD,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,GAAG,CAAC,CAAC;gBAEvD,8CAA8C;gBAC9C,MAAM,YAAY,GAChB,SAAS,KAAK,CAAC,CAAC,IAAI,CAAC,SAAS,KAAK,CAAC,CAAC,IAAI,SAAS,GAAG,SAAS,CAAC,CAAC;gBAClE,MAAM,YAAY,GAChB,SAAS,KAAK,CAAC,CAAC,IAAI,CAAC,SAAS,KAAK,CAAC,CAAC,IAAI,SAAS,GAAG,SAAS,CAAC,CAAC;gBAElE,IAAI,aAAa,GAAG,CAAC,CAAC,CAAC;gBACvB,IAAI,YAAY,GAAG,CAAC,CAAC;gBAErB,IAAI,YAAY,EAAE,CAAC;oBACjB,aAAa,GAAG,SAAS,CAAC;gBAC5B,CAAC;qBAAM,IAAI,YAAY,EAAE,CAAC;oBACxB,aAAa,GAAG,SAAS,CAAC;gBAC5B,CAAC;gBACD,YAAY,GAAG,iBAAiB,CAAC,MAAM,CAAC;gBAExC,IAAI,aAAa,KAAK,CAAC,CAAC,EAAE,CAAC;oBACzB,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;oBACtC,OAAO;gBACT,CAAC;gBAED,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,aAAa,CAAC,CAAC;gBAChD,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACxB,cAAc,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;gBACjC,CAAC;gBACD,MAAM,mBAAmB,GAAG,CAC1B,IAAiC,EAC5B,EAAE,CAAC,CAAC;oBACT,IAAI;oBACJ,IAAI,EAAE,KAAK;oBACX,IAAI,EAAE,KAAK;oBACX,KAAK,EAAE,KAAK;oBACZ,KAAK,EAAE,KAAK;oBACZ,QAAQ,EAAE,EAAE;iBACb,CAAC,CAAC;gBACH,IAAI,YAAY,EAAE,CAAC;oBACjB,cAAc,CAAC,SAAS,EAAE,mBAAmB,CAAC,aAAa,CAAC,CAAC,CAAC;gBAChE,CAAC;qBAAM,IAAI,YAAY,EAAE,CAAC;oBACxB,cAAc,CAAC,SAAS,EAAE,mBAAmB,CAAC,WAAW,CAAC,CAAC,CAAC;gBAC9D,CAAC;gBACD,GAAG,GAAG,aAAa,GAAG,YAAY,CAAC;YACrC,CAAC;QACH,CAAC,CAAC;QAEF,IAAI,EAAsB,CAAC;QAC3B,IAAI,cAAc,EAAE,CAAC;YACnB,EAAE,GAAG,QAAQ,CAAC,eAAe,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,CAAC,CAAC;YACzD,QAAQ,CAAC,kBAAkB,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC;YAChD,cAAc,CAAC,EAAE,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC;YAC9C,KAAK,CAAC,EAAE,CAAC,MAAM,EAAE,iBAAiB,CAAC,CAAC;QACtC,CAAC;aAAM,CAAC;YACN,EAAE,GAAG,QAAQ,CAAC,eAAe,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;YAChD,QAAQ,CAAC,kBAAkB,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;YACvC,KAAK,CAAC,EAAE,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC;QACvC,CAAC;QAED,OAAO,GAAG,EAAE;YACV,IAAI,cAAc,EAAE,CAAC;gBACnB,cAAc,CAAC,cAAc,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC;gBAC1D,KAAK,CAAC,cAAc,CAAC,MAAM,EAAE,iBAAiB,CAAC,CAAC;YAClD,CAAC;iBAAM,CAAC;gBACN,KAAK,CAAC,cAAc,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC;YACnD,CAAC;YACD,EAAE,CAAC,KAAK,EAAE,CAAC;YACX,UAAU,CAAC,KAAK,CAAC,CAAC;YAElB,yDAAyD;YACzD,IAAI,OAAO,EAAE,CAAC;gBACZ,aAAa,CAAC,OAAO,CAAC;oBACpB,IAAI,EAAE,EAAE;oBACR,IAAI,EAAE,KAAK;oBACX,IAAI,EAAE,KAAK;oBACX,KAAK,EAAE,KAAK;oBACZ,KAAK,EAAE,IAAI;oBACX,QAAQ,EAAE,WAAW,CAAC,QAAQ,EAAE;iBACjC,CAAC,CAAC;gBACH,WAAW,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAChC,CAAC;QACH,CAAC,CAAC;IACJ,CAAC,EAAE,CAAC,QAAQ,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC,CAAC;AACpC,CAAC"}