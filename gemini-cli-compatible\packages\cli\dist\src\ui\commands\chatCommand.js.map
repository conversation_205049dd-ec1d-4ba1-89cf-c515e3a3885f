{"version": 3, "file": "chatCommand.js", "sourceRoot": "", "sources": ["../../../../src/ui/commands/chatCommand.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,KAAK,UAAU,MAAM,aAAa,CAAC;AAC1C,OAAO,EAIL,WAAW,GACZ,MAAM,YAAY,CAAC;AACpB,OAAO,IAAI,MAAM,MAAM,CAAC;AACxB,OAAO,EAAwB,WAAW,EAAE,MAAM,aAAa,CAAC;AAOhE,MAAM,gBAAgB,GAAG,KAAK,EAC5B,OAAuB,EACvB,UAAmB,EACI,EAAE;IACzB,MAAM,SAAS,GAAG,OAAO,CAAC,QAAQ,CAAC,MAAM,EAAE,iBAAiB,EAAE,CAAC;IAC/D,IAAI,CAAC,SAAS,EAAE,CAAC;QACf,OAAO,EAAE,CAAC;IACZ,CAAC;IACD,IAAI,CAAC;QACH,MAAM,SAAS,GAAG,aAAa,CAAC;QAChC,MAAM,SAAS,GAAG,OAAO,CAAC;QAC1B,MAAM,KAAK,GAAG,MAAM,UAAU,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QAClD,MAAM,WAAW,GAAyC,EAAE,CAAC;QAE7D,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC3D,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;gBAC5C,MAAM,KAAK,GAAG,MAAM,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAC9C,WAAW,CAAC,IAAI,CAAC;oBACf,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC;oBACrD,KAAK,EAAE,KAAK,CAAC,KAAK;iBACnB,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CACxB,UAAU;YACR,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,KAAK,CAAC,OAAO,EAAE;YACvC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,KAAK,CAAC,OAAO,EAAE,CAC1C,CAAC;QAEF,OAAO,WAAW,CAAC;IACrB,CAAC;IAAC,OAAO,IAAI,EAAE,CAAC;QACd,OAAO,EAAE,CAAC;IACZ,CAAC;AACH,CAAC,CAAC;AAEF,MAAM,WAAW,GAAiB;IAChC,IAAI,EAAE,MAAM;IACZ,WAAW,EAAE,qCAAqC;IAClD,IAAI,EAAE,WAAW,CAAC,QAAQ;IAC1B,MAAM,EAAE,KAAK,EAAE,OAAO,EAAgC,EAAE;QACtD,MAAM,WAAW,GAAG,MAAM,gBAAgB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAC3D,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC7B,OAAO;gBACL,IAAI,EAAE,SAAS;gBACf,WAAW,EAAE,MAAM;gBACnB,OAAO,EAAE,0CAA0C;aACpD,CAAC;QACJ,CAAC;QAED,IAAI,OAAO,GAAG,kCAAkC,CAAC;QACjD,KAAK,MAAM,IAAI,IAAI,WAAW,EAAE,CAAC;YAC/B,OAAO,IAAI,iBAAiB,IAAI,CAAC,IAAI,aAAa,CAAC;QACrD,CAAC;QACD,OAAO,IAAI,sDAAsD,CAAC;QAClE,OAAO;YACL,IAAI,EAAE,SAAS;YACf,WAAW,EAAE,MAAM;YACnB,OAAO,EAAE,OAAO;SACjB,CAAC;IACJ,CAAC;CACF,CAAC;AAEF,MAAM,WAAW,GAAiB;IAChC,IAAI,EAAE,MAAM;IACZ,WAAW,EACT,wEAAwE;IAC1E,IAAI,EAAE,WAAW,CAAC,QAAQ;IAC1B,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,EAAgC,EAAE;QAC5D,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;QACxB,IAAI,CAAC,GAAG,EAAE,CAAC;YACT,OAAO;gBACL,IAAI,EAAE,SAAS;gBACf,WAAW,EAAE,OAAO;gBACpB,OAAO,EAAE,sCAAsC;aAChD,CAAC;QACJ,CAAC;QAED,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC,QAAQ,CAAC;QAC5C,MAAM,MAAM,CAAC,UAAU,EAAE,CAAC;QAC1B,MAAM,IAAI,GAAG,MAAM,MAAM,EAAE,eAAe,EAAE,EAAE,OAAO,EAAE,CAAC;QACxD,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,OAAO;gBACL,IAAI,EAAE,SAAS;gBACf,WAAW,EAAE,OAAO;gBACpB,OAAO,EAAE,gDAAgD;aAC1D,CAAC;QACJ,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;QAClC,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACvB,MAAM,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;YAC1C,OAAO;gBACL,IAAI,EAAE,SAAS;gBACf,WAAW,EAAE,MAAM;gBACnB,OAAO,EAAE,2CAA2C,GAAG,GAAG;aAC3D,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,OAAO;gBACL,IAAI,EAAE,SAAS;gBACf,WAAW,EAAE,MAAM;gBACnB,OAAO,EAAE,gCAAgC;aAC1C,CAAC;QACJ,CAAC;IACH,CAAC;CACF,CAAC;AAEF,MAAM,aAAa,GAAiB;IAClC,IAAI,EAAE,QAAQ;IACd,QAAQ,EAAE,CAAC,MAAM,CAAC;IAClB,WAAW,EACT,oEAAoE;IACtE,IAAI,EAAE,WAAW,CAAC,QAAQ;IAC1B,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE;QAC9B,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;QACxB,IAAI,CAAC,GAAG,EAAE,CAAC;YACT,OAAO;gBACL,IAAI,EAAE,SAAS;gBACf,WAAW,EAAE,OAAO;gBACpB,OAAO,EAAE,wCAAwC;aAClD,CAAC;QACJ,CAAC;QAED,MAAM,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC,QAAQ,CAAC;QACpC,MAAM,MAAM,CAAC,UAAU,EAAE,CAAC;QAC1B,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;QAEtD,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC9B,OAAO;gBACL,IAAI,EAAE,SAAS;gBACf,WAAW,EAAE,MAAM;gBACnB,OAAO,EAAE,uCAAuC,GAAG,GAAG;aACvD,CAAC;QACJ,CAAC;QAED,MAAM,OAAO,GAAmC;YAC9C,IAAI,EAAE,WAAW,CAAC,IAAI;YACtB,KAAK,EAAE,WAAW,CAAC,MAAM;SAC1B,CAAC;QAEF,MAAM,SAAS,GAA2B,EAAE,CAAC;QAC7C,IAAI,eAAe,GAAG,KAAK,CAAC;QAC5B,IAAI,CAAC,GAAG,CAAC,CAAC;QAEV,KAAK,MAAM,IAAI,IAAI,YAAY,EAAE,CAAC;YAChC,CAAC,IAAI,CAAC,CAAC;YACP,MAAM,IAAI,GACR,IAAI,CAAC,KAAK;gBACR,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;iBACxB,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;iBAClB,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC;YACpB,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,SAAS;YACX,CAAC;YACD,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,sBAAsB,CAAC,EAAE,CAAC;gBAClD,eAAe,GAAG,IAAI,CAAC;YACzB,CAAC;YACD,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;gBAC9B,SAAS,CAAC,IAAI,CAAC;oBACb,IAAI,EAAE,CAAC,IAAI,CAAC,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,WAAW,CAAC,MAAM;oBAC7D,IAAI;iBACmB,CAAC,CAAC;YAC7B,CAAC;QACH,CAAC;QACD,OAAO;YACL,IAAI,EAAE,cAAc;YACpB,OAAO,EAAE,SAAS;YAClB,aAAa,EAAE,YAAY;SAC5B,CAAC;IACJ,CAAC;IACD,UAAU,EAAE,KAAK,EAAE,OAAO,EAAE,UAAU,EAAE,EAAE;QACxC,MAAM,WAAW,GAAG,MAAM,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QAC1D,OAAO,WAAW;aACf,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC;aACxB,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC;IACnD,CAAC;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,WAAW,GAAiB;IACvC,IAAI,EAAE,MAAM;IACZ,WAAW,EAAE,8BAA8B;IAC3C,IAAI,EAAE,WAAW,CAAC,QAAQ;IAC1B,WAAW,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,aAAa,CAAC;CACvD,CAAC"}