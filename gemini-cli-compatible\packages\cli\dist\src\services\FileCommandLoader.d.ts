/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
import { Config } from '@inkbytefo/s647-core';
import { ICommandLoader } from './types.js';
import { SlashCommand } from '../ui/commands/types.js';
/**
 * Discovers and loads custom slash commands from .toml files in both the
 * user's global config directory and the current project's directory.
 *
 * This loader is responsible for:
 * - Recursively scanning command directories.
 * - Parsing and validating TOML files.
 * - Adapting valid definitions into executable SlashCommand objects.
 * - Handling file system errors and malformed files gracefully.
 */
export declare class FileCommandLoader implements ICommandLoader {
    private readonly config;
    private readonly projectRoot;
    constructor(config: Config | null);
    /**
     * Loads all commands, applying the precedence rule where project-level
     * commands override user-level commands with the same name.
     * @param signal An AbortSignal to cancel the loading process.
     * @returns A promise that resolves to an array of loaded SlashCommands.
     */
    loadCommands(signal: AbortSignal): Promise<SlashCommand[]>;
    /**
     * Parses a single .toml file and transforms it into a SlashCommand object.
     * @param filePath The absolute path to the .toml file.
     * @param baseDir The root command directory for name calculation.
     * @returns A promise resolving to a SlashCommand, or null if the file is invalid.
     */
    private parseAndAdaptFile;
}
