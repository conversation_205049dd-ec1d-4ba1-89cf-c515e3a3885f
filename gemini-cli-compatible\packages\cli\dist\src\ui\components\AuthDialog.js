import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
import { useState, useEffect } from 'react';
import { Box, Text, useInput } from 'ink';
import { Colors } from '../colors.js';
import { RadioButtonSelect } from './shared/RadioButtonSelect.js';
import { SettingScope } from '../../config/settings.js';
import { AuthType } from '@inkbytefo/s647-core';
import { validateAuthMethod, getAllProviderStatuses, suggestBestProvider, } from '../../config/auth.js';
function parseDefaultAuthType(defaultAuthType) {
    if (defaultAuthType &&
        Object.values(AuthType).includes(defaultAuthType)) {
        return defaultAuthType;
    }
    return null;
}
export function AuthDialog({ onSelect, settings, initialErrorMessage, }) {
    const [errorMessage, setErrorMessage] = useState(initialErrorMessage || null);
    const [providerStatuses, setProviderStatuses] = useState([]);
    const [loading, setLoading] = useState(true);
    const [suggestedProvider, setSuggestedProvider] = useState();
    useEffect(() => {
        const loadProviderStatuses = async () => {
            try {
                const statuses = await getAllProviderStatuses();
                setProviderStatuses(statuses);
                const suggested = await suggestBestProvider();
                setSuggestedProvider(suggested);
                // Show helpful message about .env configuration
                if (process.env.GEMINI_PROVIDER && suggested) {
                    setErrorMessage(`Found .env configuration for ${suggested}. This provider is recommended.`);
                }
                else if (statuses.some((s) => s.available)) {
                    const availableCount = statuses.filter((s) => s.available).length;
                    setErrorMessage(`Found ${availableCount} available provider(s) with valid API keys.`);
                }
                else {
                    setErrorMessage('No valid API keys found. You can use Google OAuth or add API keys to your .env file.');
                }
            }
            catch (error) {
                setErrorMessage(`Error loading provider information: ${error}`);
            }
            finally {
                setLoading(false);
            }
        };
        loadProviderStatuses();
    }, []);
    const items = providerStatuses.map((status) => {
        const statusIcon = status.available ? '✓' : status.hasApiKey ? '⚠' : '✗';
        return {
            label: `${statusIcon} ${status.displayName}`,
            value: status.authType,
            description: status.available
                ? status.description
                : status.errorMessage || 'Not available',
            available: status.available,
            recommended: status.provider === suggestedProvider,
        };
    });
    const initialAuthIndex = items.findIndex((item) => {
        // First check if there's a recommended provider
        if (item.recommended) {
            return true;
        }
        // Then check user's current selection
        if (settings.merged.selectedAuthType) {
            return item.value === settings.merged.selectedAuthType;
        }
        // Check environment default
        const defaultAuthType = parseDefaultAuthType(process.env.GEMINI_DEFAULT_AUTH_TYPE);
        if (defaultAuthType) {
            return item.value === defaultAuthType;
        }
        // Default to first available provider or Google OAuth
        return item.available || item.value === AuthType.LOGIN_WITH_GOOGLE;
    });
    const handleAuthSelect = (authMethod) => {
        const error = validateAuthMethod(authMethod);
        if (error) {
            setErrorMessage(error);
        }
        else {
            setErrorMessage(null);
            onSelect(authMethod, SettingScope.User);
        }
    };
    useInput((_input, key) => {
        if (key.escape) {
            // Prevent exit if there is an error message.
            // This means they user is not authenticated yet.
            if (errorMessage) {
                return;
            }
            if (settings.merged.selectedAuthType === undefined) {
                // Prevent exiting if no auth method is set
                setErrorMessage('You must select an auth method to proceed. Press Ctrl+C twice to exit.');
                return;
            }
            onSelect(undefined, SettingScope.User);
        }
    });
    if (loading) {
        return (_jsxs(Box, { borderStyle: "round", borderColor: Colors.Gray, flexDirection: "column", padding: 1, width: "100%", children: [_jsx(Text, { bold: true, children: "Loading providers..." }), _jsx(Box, { marginTop: 1, children: _jsx(Text, { children: "Checking available authentication methods..." }) })] }));
    }
    return (_jsxs(Box, { borderStyle: "round", borderColor: Colors.Gray, flexDirection: "column", padding: 1, width: "100%", children: [_jsx(Text, { bold: true, children: "Authentication Setup" }), _jsx(Box, { marginTop: 1, children: _jsx(Text, { children: "Choose your preferred authentication method:" }) }), _jsx(Box, { marginTop: 1, flexDirection: "column", children: _jsx(Text, { dimColor: true, children: "Status: \u2713 Available \u26A0 Invalid Key \u2717 Missing Key" }) }), _jsx(Box, { marginTop: 1, children: _jsx(RadioButtonSelect, { items: items.map((item) => ({
                        label: item.label,
                        value: item.value,
                    })), initialIndex: Math.max(0, initialAuthIndex), onSelect: handleAuthSelect, isFocused: true }) }), _jsx(Box, { marginTop: 1, flexDirection: "column", children: items.map((item, index) => index === Math.max(0, initialAuthIndex) && (_jsxs(Box, { flexDirection: "column", children: [_jsx(Text, { dimColor: true, children: item.description }), !item.available &&
                            item.value !== AuthType.LOGIN_WITH_GOOGLE && (_jsx(Text, { color: Colors.AccentYellow, children: "\uD83D\uDCA1 Add your API key to .env file to use this provider" }))] }, item.value))) }), errorMessage && (_jsx(Box, { marginTop: 1, children: _jsx(Text, { color: errorMessage.includes('Found')
                        ? Colors.AccentGreen
                        : Colors.AccentRed, children: errorMessage }) })), _jsx(Box, { marginTop: 1, children: _jsx(Text, { color: Colors.Gray, children: "(Use Enter to select)" }) }), _jsx(Box, { marginTop: 1, children: _jsx(Text, { children: "Terms of Services and Privacy Notice for Gemini CLI" }) }), _jsx(Box, { marginTop: 1, children: _jsx(Text, { color: Colors.AccentBlue, children: 'https://github.com/google-gemini/gemini-cli/blob/main/docs/tos-privacy.md' }) })] }));
}
//# sourceMappingURL=AuthDialog.js.map