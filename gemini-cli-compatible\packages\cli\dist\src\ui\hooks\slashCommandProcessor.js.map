{"version": 3, "file": "slashCommandProcessor.js", "sourceRoot": "", "sources": ["../../../../src/ui/hooks/slashCommandProcessor.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,WAAW,EAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,MAAM,OAAO,CAAC;AAElE,OAAO,OAAO,MAAM,cAAc,CAAC;AAEnC,OAAO,EAAE,cAAc,EAAE,MAAM,qBAAqB,CAAC;AACrD,OAAO,EAAU,UAAU,EAAE,MAAM,EAAE,MAAM,sBAAsB,CAAC;AAClE,OAAO,EAAE,eAAe,EAAE,MAAM,+BAA+B,CAAC;AAChE,OAAO,EAEL,WAAW,GAIZ,MAAM,aAAa,CAAC;AAGrB,OAAO,EAAE,cAAc,EAAE,MAAM,kCAAkC,CAAC;AAClE,OAAO,EAAE,oBAAoB,EAAE,MAAM,wCAAwC,CAAC;AAC9E,OAAO,EAAE,iBAAiB,EAAE,MAAM,qCAAqC,CAAC;AAExE;;GAEG;AACH,MAAM,CAAC,MAAM,wBAAwB,GAAG,CACtC,MAAqB,EACrB,QAAwB,EACxB,OAA2C,EAC3C,UAAiD,EACjD,WAAmD,EACnD,aAAyB,EACzB,WAA0D,EAC1D,cAAyC,EACzC,eAA2B,EAC3B,cAA0B,EAC1B,gBAA4B,EAC5B,eAA2B,EAC3B,mBAAqD,EACrD,iBAA6B,EAC7B,EAAE;IACF,MAAM,OAAO,GAAG,eAAe,EAAE,CAAC;IAClC,MAAM,CAAC,QAAQ,EAAE,WAAW,CAAC,GAAG,QAAQ,CAA0B,EAAE,CAAC,CAAC;IACtE,MAAM,UAAU,GAAG,OAAO,CAAC,GAAG,EAAE;QAC9B,IAAI,CAAC,MAAM,EAAE,cAAc,EAAE,EAAE,CAAC;YAC9B,OAAO;QACT,CAAC;QACD,OAAO,IAAI,UAAU,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC,CAAC;IACjD,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;IAEb,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,EAAE;QAC1B,MAAM,CAAC,GAAG,IAAI,MAAM,CAAC,MAAM,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC;QACnD,mEAAmE;QACnE,qEAAqE;QACrE,OAAO,CAAC,CAAC;IACX,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;IAEb,MAAM,CAAC,yBAAyB,EAAE,yBAAyB,CAAC,GAC1D,cAAc,CAA8B,IAAI,CAAC,CAAC;IAEpD,MAAM,mBAAmB,GAAG,OAAO,CAAC,GAAG,EAAE;QACvC,MAAM,KAAK,GAA2B,EAAE,CAAC;QACzC,IAAI,yBAAyB,CAAC,OAAO,IAAI,IAAI,EAAE,CAAC;YAC9C,KAAK,CAAC,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC,CAAC;QAChD,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC,EAAE,CAAC,yBAAyB,CAAC,CAAC,CAAC;IAEhC,MAAM,UAAU,GAAG,WAAW,CAC5B,CAAC,OAAgB,EAAE,EAAE;QACnB,0CAA0C;QAC1C,IAAI,kBAAwC,CAAC;QAC7C,IAAI,OAAO,CAAC,IAAI,KAAK,WAAW,CAAC,KAAK,EAAE,CAAC;YACvC,kBAAkB,GAAG;gBACnB,IAAI,EAAE,OAAO;gBACb,UAAU,EAAE,OAAO,CAAC,UAAU;gBAC9B,SAAS,EAAE,OAAO,CAAC,SAAS;gBAC5B,UAAU,EAAE,OAAO,CAAC,UAAU;gBAC9B,YAAY,EAAE,OAAO,CAAC,YAAY;gBAClC,gBAAgB,EAAE,OAAO,CAAC,gBAAgB;gBAC1C,UAAU,EAAE,OAAO,CAAC,UAAU;aAC/B,CAAC;QACJ,CAAC;aAAM,IAAI,OAAO,CAAC,IAAI,KAAK,WAAW,CAAC,KAAK,EAAE,CAAC;YAC9C,kBAAkB,GAAG;gBACnB,IAAI,EAAE,OAAO;gBACb,QAAQ,EAAE,OAAO,CAAC,QAAQ;aAC3B,CAAC;QACJ,CAAC;aAAM,IAAI,OAAO,CAAC,IAAI,KAAK,WAAW,CAAC,WAAW,EAAE,CAAC;YACpD,kBAAkB,GAAG;gBACnB,IAAI,EAAE,aAAa;aACpB,CAAC;QACJ,CAAC;aAAM,IAAI,OAAO,CAAC,IAAI,KAAK,WAAW,CAAC,UAAU,EAAE,CAAC;YACnD,kBAAkB,GAAG;gBACnB,IAAI,EAAE,YAAY;aACnB,CAAC;QACJ,CAAC;aAAM,IAAI,OAAO,CAAC,IAAI,KAAK,WAAW,CAAC,IAAI,EAAE,CAAC;YAC7C,kBAAkB,GAAG;gBACnB,IAAI,EAAE,MAAM;gBACZ,QAAQ,EAAE,OAAO,CAAC,QAAQ;aAC3B,CAAC;QACJ,CAAC;aAAM,IAAI,OAAO,CAAC,IAAI,KAAK,WAAW,CAAC,WAAW,EAAE,CAAC;YACpD,kBAAkB,GAAG;gBACnB,IAAI,EAAE,aAAa;gBACnB,WAAW,EAAE,OAAO,CAAC,WAAW;aACjC,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,kBAAkB,GAAG;gBACnB,IAAI,EAAE,OAAO,CAAC,IAAI;gBAClB,IAAI,EAAE,OAAO,CAAC,OAAO;aACtB,CAAC;QACJ,CAAC;QACD,OAAO,CAAC,kBAAkB,EAAE,OAAO,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC;IAC3D,CAAC,EACD,CAAC,OAAO,CAAC,CACV,CAAC;IAEF,MAAM,cAAc,GAAG,OAAO,CAC5B,GAAmB,EAAE,CAAC,CAAC;QACrB,QAAQ,EAAE;YACR,MAAM;YACN,QAAQ;YACR,GAAG,EAAE,UAAU;YACf,MAAM;SACP;QACD,EAAE,EAAE;YACF,OAAO;YACP,KAAK,EAAE,GAAG,EAAE;gBACV,UAAU,EAAE,CAAC;gBACb,OAAO,CAAC,KAAK,EAAE,CAAC;gBAChB,aAAa,EAAE,CAAC;YAClB,CAAC;YACD,WAAW;YACX,eAAe,EAAE,cAAc;YAC/B,WAAW,EAAE,yBAAyB,CAAC,OAAO;YAC9C,cAAc,EAAE,yBAAyB;YACzC,eAAe;SAChB;QACD,OAAO,EAAE;YACP,KAAK,EAAE,OAAO,CAAC,KAAK;SACrB;KACF,CAAC,EACF;QACE,MAAM;QACN,QAAQ;QACR,UAAU;QACV,MAAM;QACN,WAAW;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,OAAO,CAAC,KAAK;QACb,cAAc;QACd,yBAAyB;QACzB,yBAAyB;QACzB,eAAe;KAChB,CACF,CAAC;IAEF,SAAS,CAAC,GAAG,EAAE;QACb,MAAM,UAAU,GAAG,IAAI,eAAe,EAAE,CAAC;QACzC,MAAM,IAAI,GAAG,KAAK,IAAI,EAAE;YACtB,MAAM,OAAO,GAAG;gBACd,IAAI,oBAAoB,CAAC,MAAM,CAAC;gBAChC,IAAI,iBAAiB,CAAC,MAAM,CAAC;aAC9B,CAAC;YACF,MAAM,cAAc,GAAG,MAAM,cAAc,CAAC,MAAM,CAChD,OAAO,EACP,UAAU,CAAC,MAAM,CAClB,CAAC;YACF,WAAW,CAAC,cAAc,CAAC,WAAW,EAAE,CAAC,CAAC;QAC5C,CAAC,CAAC;QAEF,IAAI,EAAE,CAAC;QAEP,OAAO,GAAG,EAAE;YACV,UAAU,CAAC,KAAK,EAAE,CAAC;QACrB,CAAC,CAAC;IACJ,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;IAEb,MAAM,kBAAkB,GAAG,WAAW,CACpC,KAAK,EACH,QAAuB,EACuB,EAAE;QAChD,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,CAAC;YACjC,OAAO,KAAK,CAAC;QACf,CAAC;QAED,MAAM,OAAO,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;QAChC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;YACzD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,MAAM,oBAAoB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACxC,OAAO,CAAC,EAAE,IAAI,EAAE,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE,oBAAoB,CAAC,CAAC;QAEzE,MAAM,KAAK,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QACvD,MAAM,WAAW,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,oDAAoD;QAEhG,IAAI,eAAe,GAAG,QAAQ,CAAC;QAC/B,IAAI,gBAA0C,CAAC;QAC/C,IAAI,SAAS,GAAG,CAAC,CAAC;QAElB,KAAK,MAAM,IAAI,IAAI,WAAW,EAAE,CAAC;YAC/B,wEAAwE;YACxE,gEAAgE;YAChE,uEAAuE;YACvE,qEAAqE;YACrE,kEAAkE;YAElE,oEAAoE;YACpE,IAAI,YAAY,GAAG,eAAe,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC;YAEpE,+DAA+D;YAC/D,IAAI,CAAC,YAAY,EAAE,CAAC;gBAClB,YAAY,GAAG,eAAe,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,CAC1C,GAAG,CAAC,QAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC,CAC7B,CAAC;YACJ,CAAC;YAED,IAAI,YAAY,EAAE,CAAC;gBACjB,gBAAgB,GAAG,YAAY,CAAC;gBAChC,SAAS,EAAE,CAAC;gBACZ,IAAI,YAAY,CAAC,WAAW,EAAE,CAAC;oBAC7B,eAAe,GAAG,YAAY,CAAC,WAAW,CAAC;gBAC7C,CAAC;qBAAM,CAAC;oBACN,MAAM;gBACR,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,MAAM;YACR,CAAC;QACH,CAAC;QAED,IAAI,gBAAgB,EAAE,CAAC;YACrB,MAAM,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAE9C,IAAI,gBAAgB,CAAC,MAAM,EAAE,CAAC;gBAC5B,MAAM,MAAM,GAAG,MAAM,gBAAgB,CAAC,MAAM,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;gBAEnE,IAAI,MAAM,EAAE,CAAC;oBACX,QAAQ,MAAM,CAAC,IAAI,EAAE,CAAC;wBACpB,KAAK,MAAM;4BACT,OAAO;gCACL,IAAI,EAAE,eAAe;gCACrB,QAAQ,EAAE,MAAM,CAAC,QAAQ;gCACzB,QAAQ,EAAE,MAAM,CAAC,QAAQ;6BAC1B,CAAC;wBACJ,KAAK,SAAS;4BACZ,OAAO,CACL;gCACE,IAAI,EACF,MAAM,CAAC,WAAW,KAAK,OAAO;oCAC5B,CAAC,CAAC,WAAW,CAAC,KAAK;oCACnB,CAAC,CAAC,WAAW,CAAC,IAAI;gCACtB,IAAI,EAAE,MAAM,CAAC,OAAO;6BACrB,EACD,IAAI,CAAC,GAAG,EAAE,CACX,CAAC;4BACF,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;wBAC7B,KAAK,QAAQ;4BACX,QAAQ,MAAM,CAAC,MAAM,EAAE,CAAC;gCACtB,KAAK,MAAM;oCACT,WAAW,CAAC,IAAI,CAAC,CAAC;oCAClB,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;gCAC7B,KAAK,MAAM;oCACT,cAAc,EAAE,CAAC;oCACjB,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;gCAC7B,KAAK,OAAO;oCACV,eAAe,EAAE,CAAC;oCAClB,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;gCAC7B,KAAK,QAAQ;oCACX,gBAAgB,EAAE,CAAC;oCACnB,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;gCAC7B,KAAK,SAAS;oCACZ,iBAAiB,EAAE,CAAC;oCACpB,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;gCAC7B,OAAO,CAAC,CAAC,CAAC;oCACR,MAAM,SAAS,GAAU,MAAM,CAAC,MAAM,CAAC;oCACvC,MAAM,IAAI,KAAK,CACb,mCAAmC,SAAS,EAAE,CAC/C,CAAC;gCACJ,CAAC;4BACH,CAAC;wBACH,KAAK,cAAc,CAAC,CAAC,CAAC;4BACpB,MAAM,MAAM;gCACV,EAAE,eAAe,EAAE;gCACnB,EAAE,UAAU,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;4BACrC,cAAc,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC;4BAC1B,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;gCACrC,cAAc,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;4BACzC,CAAC,CAAC,CAAC;4BACH,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;wBAC7B,CAAC;wBACD,KAAK,MAAM;4BACT,mBAAmB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;4BACrC,UAAU,CAAC,GAAG,EAAE;gCACd,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;4BAClB,CAAC,EAAE,GAAG,CAAC,CAAC;4BACR,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;wBAE7B,KAAK,eAAe;4BAClB,OAAO;gCACL,IAAI,EAAE,eAAe;gCACrB,OAAO,EAAE,MAAM,CAAC,OAAO;6BACxB,CAAC;wBACJ,OAAO,CAAC,CAAC,CAAC;4BACR,MAAM,SAAS,GAAU,MAAM,CAAC;4BAChC,MAAM,IAAI,KAAK,CAAC,mCAAmC,SAAS,EAAE,CAAC,CAAC;wBAClE,CAAC;oBACH,CAAC;gBACH,CAAC;gBAED,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;YAC7B,CAAC;iBAAM,IAAI,gBAAgB,CAAC,WAAW,EAAE,CAAC;gBACxC,MAAM,QAAQ,GAAG,aAAa,gBAAgB,CAAC,IAAI,wCAAwC,gBAAgB,CAAC,WAAW;qBACpH,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,OAAO,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,WAAW,IAAI,EAAE,EAAE,CAAC;qBACtD,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;gBAChB,UAAU,CAAC;oBACT,IAAI,EAAE,WAAW,CAAC,IAAI;oBACtB,OAAO,EAAE,QAAQ;oBACjB,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB,CAAC,CAAC;gBACH,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;YAC7B,CAAC;QACH,CAAC;QAED,UAAU,CAAC;YACT,IAAI,EAAE,WAAW,CAAC,KAAK;YACvB,OAAO,EAAE,oBAAoB,OAAO,EAAE;YACtC,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC,CAAC;QACH,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;IAC7B,CAAC,EACD;QACE,MAAM;QACN,OAAO;QACP,WAAW;QACX,cAAc;QACd,QAAQ;QACR,cAAc;QACd,UAAU;QACV,eAAe;QACf,iBAAiB;QACjB,gBAAgB;QAChB,mBAAmB;KACpB,CACF,CAAC;IAEF,OAAO;QACL,kBAAkB;QAClB,aAAa,EAAE,QAAQ;QACvB,mBAAmB;QACnB,cAAc;KACf,CAAC;AACJ,CAAC,CAAC"}