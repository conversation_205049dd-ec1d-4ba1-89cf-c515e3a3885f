{"version": 3, "file": "docsCommand.js", "sourceRoot": "", "sources": ["../../../../src/ui/commands/docsCommand.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,IAAI,MAAM,MAAM,CAAC;AACxB,OAAO,OAAO,MAAM,cAAc,CAAC;AACnC,OAAO,EAGL,WAAW,GACZ,MAAM,YAAY,CAAC;AACpB,OAAO,EAAE,WAAW,EAAE,MAAM,aAAa,CAAC;AAE1C,MAAM,CAAC,MAAM,WAAW,GAAiB;IACvC,IAAI,EAAE,MAAM;IACZ,WAAW,EAAE,oDAAoD;IACjE,IAAI,EAAE,WAAW,CAAC,QAAQ;IAC1B,MAAM,EAAE,KAAK,EAAE,OAAuB,EAAiB,EAAE;QACvD,MAAM,OAAO,GAAG,iCAAiC,CAAC;QAElD,IAAI,OAAO,CAAC,GAAG,CAAC,OAAO,IAAI,OAAO,CAAC,GAAG,CAAC,OAAO,KAAK,cAAc,EAAE,CAAC;YAClE,OAAO,CAAC,EAAE,CAAC,OAAO,CAChB;gBACE,IAAI,EAAE,WAAW,CAAC,IAAI;gBACtB,IAAI,EAAE,6EAA6E,OAAO,EAAE;aAC7F,EACD,IAAI,CAAC,GAAG,EAAE,CACX,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,EAAE,CAAC,OAAO,CAChB;gBACE,IAAI,EAAE,WAAW,CAAC,IAAI;gBACtB,IAAI,EAAE,0CAA0C,OAAO,EAAE;aAC1D,EACD,IAAI,CAAC,GAAG,EAAE,CACX,CAAC;YACF,MAAM,IAAI,CAAC,OAAO,CAAC,CAAC;QACtB,CAAC;IACH,CAAC;CACF,CAAC"}