class S647 < Formula
  desc "Advanced AI-Powered CLI Agent - Intelligent Code Assistant"
  homepage "https://github.com/inkbytefo/S647-cli"
  url "https://registry.npmjs.org/@inkbytefo/s647/-/s647-1.0.0.tgz"
  sha256 "REPLACE_WITH_ACTUAL_SHA256"
  license "Apache-2.0"

  depends_on "node"

  def install
    system "npm", "install", *Language::Node.std_npm_install_args(libexec)
    bin.install_symlink Dir["#{libexec}/bin/*"]
  end

  test do
    assert_match "S647", shell_output("#{bin}/s647 --version")
  end
end
