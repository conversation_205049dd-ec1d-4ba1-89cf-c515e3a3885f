{"version": 3, "file": "BuiltinCommandLoader.js", "sourceRoot": "", "sources": ["../../../src/services/BuiltinCommandLoader.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAKH,OAAO,EAAE,YAAY,EAAE,MAAM,gCAAgC,CAAC;AAC9D,OAAO,EAAE,WAAW,EAAE,MAAM,+BAA+B,CAAC;AAC5D,OAAO,EAAE,UAAU,EAAE,MAAM,8BAA8B,CAAC;AAC1D,OAAO,EAAE,WAAW,EAAE,MAAM,+BAA+B,CAAC;AAC5D,OAAO,EAAE,YAAY,EAAE,MAAM,gCAAgC,CAAC;AAC9D,OAAO,EAAE,eAAe,EAAE,MAAM,mCAAmC,CAAC;AACpE,OAAO,EAAE,WAAW,EAAE,MAAM,+BAA+B,CAAC;AAC5D,OAAO,EAAE,YAAY,EAAE,MAAM,gCAAgC,CAAC;AAC9D,OAAO,EAAE,WAAW,EAAE,MAAM,+BAA+B,CAAC;AAC5D,OAAO,EAAE,aAAa,EAAE,MAAM,iCAAiC,CAAC;AAChE,OAAO,EAAE,iBAAiB,EAAE,MAAM,qCAAqC,CAAC;AACxE,OAAO,EAAE,WAAW,EAAE,MAAM,+BAA+B,CAAC;AAC5D,OAAO,EAAE,UAAU,EAAE,MAAM,8BAA8B,CAAC;AAC1D,OAAO,EAAE,WAAW,EAAE,MAAM,+BAA+B,CAAC;AAC5D,OAAO,EAAE,UAAU,EAAE,MAAM,8BAA8B,CAAC;AAC1D,OAAO,EAAE,aAAa,EAAE,MAAM,iCAAiC,CAAC;AAChE,OAAO,EAAE,cAAc,EAAE,MAAM,kCAAkC,CAAC;AAClE,OAAO,EAAE,WAAW,EAAE,MAAM,+BAA+B,CAAC;AAC5D,OAAO,EAAE,cAAc,EAAE,MAAM,kCAAkC,CAAC;AAClE,OAAO,EAAE,YAAY,EAAE,MAAM,gCAAgC,CAAC;AAC9D,OAAO,EAAE,YAAY,EAAE,MAAM,gCAAgC,CAAC;AAC9D,OAAO,EAAE,YAAY,EAAE,MAAM,gCAAgC,CAAC;AAE9D;;;GAGG;AACH,MAAM,OAAO,oBAAoB;IACX;IAApB,YAAoB,MAAqB;QAArB,WAAM,GAAN,MAAM,CAAe;IAAG,CAAC;IAE7C;;;;;;OAMG;IACH,KAAK,CAAC,YAAY,CAAC,OAAoB;QACrC,MAAM,cAAc,GAA+B;YACjD,YAAY;YACZ,WAAW;YACX,UAAU;YACV,WAAW;YACX,YAAY;YACZ,eAAe;YACf,WAAW;YACX,YAAY;YACZ,WAAW;YACX,aAAa;YACb,iBAAiB;YACjB,WAAW;YACX,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC;YACvB,WAAW;YACX,UAAU;YACV,aAAa;YACb,cAAc;YACd,WAAW;YACX,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC;YAC3B,YAAY;YACZ,YAAY;YACZ,YAAY;SACb,CAAC;QAEF,OAAO,cAAc,CAAC,MAAM,CAAC,CAAC,GAAG,EAAuB,EAAE,CAAC,GAAG,KAAK,IAAI,CAAC,CAAC;IAC3E,CAAC;CACF"}