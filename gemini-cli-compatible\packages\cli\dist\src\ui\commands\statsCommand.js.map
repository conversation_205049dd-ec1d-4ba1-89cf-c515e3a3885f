{"version": 3, "file": "statsCommand.js", "sourceRoot": "", "sources": ["../../../../src/ui/commands/statsCommand.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,WAAW,EAAoB,MAAM,aAAa,CAAC;AAC5D,OAAO,EAAE,cAAc,EAAE,MAAM,wBAAwB,CAAC;AACxD,OAAO,EAGL,WAAW,GACZ,MAAM,YAAY,CAAC;AAEpB,MAAM,CAAC,MAAM,YAAY,GAAiB;IACxC,IAAI,EAAE,OAAO;IACb,QAAQ,EAAE,CAAC,OAAO,CAAC;IACnB,WAAW,EAAE,kDAAkD;IAC/D,IAAI,EAAE,WAAW,CAAC,QAAQ;IAC1B,MAAM,EAAE,CAAC,OAAuB,EAAE,EAAE;QAClC,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,EAAE,gBAAgB,EAAE,GAAG,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC;QACnD,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACtB,OAAO,CAAC,EAAE,CAAC,OAAO,CAChB;gBACE,IAAI,EAAE,WAAW,CAAC,KAAK;gBACvB,IAAI,EAAE,4DAA4D;aACnE,EACD,IAAI,CAAC,GAAG,EAAE,CACX,CAAC;YACF,OAAO;QACT,CAAC;QACD,MAAM,YAAY,GAAG,GAAG,CAAC,OAAO,EAAE,GAAG,gBAAgB,CAAC,OAAO,EAAE,CAAC;QAEhE,MAAM,SAAS,GAAqB;YAClC,IAAI,EAAE,WAAW,CAAC,KAAK;YACvB,QAAQ,EAAE,cAAc,CAAC,YAAY,CAAC;SACvC,CAAC;QAEF,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;IAC5C,CAAC;IACD,WAAW,EAAE;QACX;YACE,IAAI,EAAE,OAAO;YACb,WAAW,EAAE,uCAAuC;YACpD,IAAI,EAAE,WAAW,CAAC,QAAQ;YAC1B,MAAM,EAAE,CAAC,OAAuB,EAAE,EAAE;gBAClC,OAAO,CAAC,EAAE,CAAC,OAAO,CAChB;oBACE,IAAI,EAAE,WAAW,CAAC,WAAW;iBAC9B,EACD,IAAI,CAAC,GAAG,EAAE,CACX,CAAC;YACJ,CAAC;SACF;QACD;YACE,IAAI,EAAE,OAAO;YACb,WAAW,EAAE,sCAAsC;YACnD,IAAI,EAAE,WAAW,CAAC,QAAQ;YAC1B,MAAM,EAAE,CAAC,OAAuB,EAAE,EAAE;gBAClC,OAAO,CAAC,EAAE,CAAC,OAAO,CAChB;oBACE,IAAI,EAAE,WAAW,CAAC,UAAU;iBAC7B,EACD,IAAI,CAAC,GAAG,EAAE,CACX,CAAC;YACJ,CAAC;SACF;KACF;CACF,CAAC"}