{"version": 3, "file": "no-color.js", "sourceRoot": "", "sources": ["../../../../src/ui/themes/no-color.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,KAAK,EAAe,MAAM,YAAY,CAAC;AAEhD,MAAM,kBAAkB,GAAgB;IACtC,IAAI,EAAE,MAAM;IACZ,UAAU,EAAE,EAAE;IACd,UAAU,EAAE,EAAE;IACd,SAAS,EAAE,EAAE;IACb,UAAU,EAAE,EAAE;IACd,YAAY,EAAE,EAAE;IAChB,UAAU,EAAE,EAAE;IACd,WAAW,EAAE,EAAE;IACf,YAAY,EAAE,EAAE;IAChB,SAAS,EAAE,EAAE;IACb,OAAO,EAAE,EAAE;IACX,IAAI,EAAE,EAAE;CACT,CAAC;AAEF,MAAM,CAAC,MAAM,YAAY,GAAU,IAAI,KAAK,CAC1C,SAAS,EACT,MAAM,EACN;IACE,IAAI,EAAE;QACJ,OAAO,EAAE,OAAO;QAChB,SAAS,EAAE,MAAM;QACjB,OAAO,EAAE,OAAO;KACjB;IACD,cAAc,EAAE,EAAE;IAClB,cAAc,EAAE,EAAE;IAClB,aAAa,EAAE,EAAE;IACjB,WAAW,EAAE,EAAE;IACf,WAAW,EAAE;QACX,cAAc,EAAE,WAAW;KAC5B;IACD,eAAe,EAAE,EAAE;IACnB,WAAW,EAAE,EAAE;IACf,aAAa,EAAE,EAAE;IACjB,YAAY,EAAE,EAAE;IAChB,aAAa,EAAE,EAAE;IACjB,kBAAkB,EAAE,EAAE;IACtB,aAAa,EAAE,EAAE;IACjB,mBAAmB,EAAE,EAAE;IACvB,YAAY,EAAE,EAAE;IAChB,eAAe,EAAE,EAAE;IACnB,YAAY,EAAE,EAAE;IAChB,aAAa,EAAE,EAAE;IACjB,cAAc,EAAE,EAAE;IAClB,cAAc,EAAE;QACd,SAAS,EAAE,QAAQ;KACpB;IACD,YAAY,EAAE;QACZ,SAAS,EAAE,QAAQ;KACpB;IACD,aAAa,EAAE,EAAE;IACjB,WAAW,EAAE,EAAE;IACf,mBAAmB,EAAE,EAAE;IACvB,UAAU,EAAE,EAAE;IACd,eAAe,EAAE,EAAE;IACnB,wBAAwB,EAAE,EAAE;IAC5B,WAAW,EAAE,EAAE;IACf,gBAAgB,EAAE,EAAE;IACpB,mBAAmB,EAAE,EAAE;IACvB,cAAc,EAAE,EAAE;IAClB,eAAe,EAAE;QACf,SAAS,EAAE,QAAQ;KACpB;IACD,aAAa,EAAE;QACb,UAAU,EAAE,MAAM;KACnB;IACD,aAAa,EAAE,EAAE;IACjB,mBAAmB,EAAE,EAAE;IACvB,kBAAkB,EAAE,EAAE;IACtB,qBAAqB,EAAE,EAAE;IACzB,oBAAoB,EAAE,EAAE;IACxB,sBAAsB,EAAE,EAAE;IAC1B,eAAe,EAAE;QACf,OAAO,EAAE,cAAc;QACvB,KAAK,EAAE,MAAM;KACd;IACD,eAAe,EAAE;QACf,OAAO,EAAE,cAAc;QACvB,KAAK,EAAE,MAAM;KACd;CACF,EACD,kBAAkB,CACnB,CAAC"}