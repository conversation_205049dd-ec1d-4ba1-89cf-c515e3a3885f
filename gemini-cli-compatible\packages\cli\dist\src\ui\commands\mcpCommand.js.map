{"version": 3, "file": "mcpCommand.js", "sourceRoot": "", "sources": ["../../../../src/ui/commands/mcpCommand.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAIL,WAAW,GAEZ,MAAM,YAAY,CAAC;AACpB,OAAO,EACL,iBAAiB,EACjB,oBAAoB,EACpB,kBAAkB,EAClB,iBAAiB,EACjB,eAAe,EACf,sBAAsB,EACtB,eAAe,GAChB,MAAM,sBAAsB,CAAC;AAC9B,OAAO,IAAI,MAAM,MAAM,CAAC;AAExB,MAAM,WAAW,GAAG,YAAY,CAAC;AACjC,MAAM,YAAY,GAAG,YAAY,CAAC;AAClC,MAAM,SAAS,GAAG,YAAY,CAAC;AAC/B,MAAM,UAAU,GAAG,YAAY,CAAC;AAChC,MAAM,UAAU,GAAG,YAAY,CAAC;AAChC,MAAM,WAAW,GAAG,WAAW,CAAC;AAEhC,MAAM,YAAY,GAAG,KAAK,EACxB,OAAuB,EACvB,gBAAyB,EACzB,UAAmB,EACnB,WAAoB,KAAK,EACU,EAAE;IACrC,MAAM,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC,QAAQ,CAAC;IACpC,IAAI,CAAC,MAAM,EAAE,CAAC;QACZ,OAAO;YACL,IAAI,EAAE,SAAS;YACf,WAAW,EAAE,OAAO;YACpB,OAAO,EAAE,oBAAoB;SAC9B,CAAC;IACJ,CAAC;IAED,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,eAAe,EAAE,CAAC;IACpD,IAAI,CAAC,YAAY,EAAE,CAAC;QAClB,OAAO;YACL,IAAI,EAAE,SAAS;YACf,WAAW,EAAE,OAAO;YACpB,OAAO,EAAE,mCAAmC;SAC7C,CAAC;IACJ,CAAC;IAED,MAAM,UAAU,GAAG,MAAM,CAAC,aAAa,EAAE,IAAI,EAAE,CAAC;IAChD,MAAM,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IAC5C,MAAM,iBAAiB,GAAG,MAAM,CAAC,oBAAoB,EAAE,IAAI,EAAE,CAAC;IAE9D,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,IAAI,iBAAiB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAC/D,MAAM,OAAO,GAAG,qCAAqC,CAAC;QACtD,IAAI,OAAO,CAAC,GAAG,CAAC,OAAO,IAAI,OAAO,CAAC,GAAG,CAAC,OAAO,KAAK,cAAc,EAAE,CAAC;YAClE,OAAO;gBACL,IAAI,EAAE,SAAS;gBACf,WAAW,EAAE,MAAM;gBACnB,OAAO,EAAE,oGAAoG,OAAO,EAAE;aACvH,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,8BAA8B;YAC9B,MAAM,IAAI,CAAC,OAAO,CAAC,CAAC;YACpB,OAAO;gBACL,IAAI,EAAE,SAAS;gBACf,WAAW,EAAE,MAAM;gBACnB,OAAO,EAAE,qEAAqE,OAAO,EAAE;aACxF,CAAC;QACJ,CAAC;IACH,CAAC;IAED,4CAA4C;IAC5C,MAAM,iBAAiB,GAAG,WAAW,CAAC,MAAM,CAC1C,CAAC,IAAI,EAAE,EAAE,CAAC,kBAAkB,CAAC,IAAI,CAAC,KAAK,eAAe,CAAC,UAAU,CAClE,CAAC;IACF,MAAM,cAAc,GAAG,oBAAoB,EAAE,CAAC;IAE9C,IAAI,OAAO,GAAG,EAAE,CAAC;IAEjB,iDAAiD;IACjD,IACE,cAAc,KAAK,iBAAiB,CAAC,WAAW;QAChD,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAC5B,CAAC;QACD,OAAO,IAAI,GAAG,YAAY,kCAAkC,iBAAiB,CAAC,MAAM,oBAAoB,WAAW,IAAI,CAAC;QACxH,OAAO,IAAI,GAAG,UAAU,oFAAoF,WAAW,MAAM,CAAC;IAChI,CAAC;IAED,OAAO,IAAI,6BAA6B,CAAC;IAEzC,MAAM,QAAQ,GAAG,YAAY,CAAC,WAAW,EAAE,CAAC;IAC5C,KAAK,MAAM,UAAU,IAAI,WAAW,EAAE,CAAC;QACrC,MAAM,WAAW,GAAG,QAAQ,CAAC,MAAM,CACjC,CAAC,IAAI,EAAE,EAAE,CACP,IAAI,YAAY,iBAAiB,IAAI,IAAI,CAAC,UAAU,KAAK,UAAU,CAC/C,CAAC;QAEzB,MAAM,MAAM,GAAG,kBAAkB,CAAC,UAAU,CAAC,CAAC;QAE9C,6CAA6C;QAC7C,IAAI,eAAe,GAAG,EAAE,CAAC;QACzB,IAAI,UAAU,GAAG,EAAE,CAAC;QACpB,QAAQ,MAAM,EAAE,CAAC;YACf,KAAK,eAAe,CAAC,SAAS;gBAC5B,eAAe,GAAG,IAAI,CAAC;gBACvB,UAAU,GAAG,OAAO,CAAC;gBACrB,MAAM;YACR,KAAK,eAAe,CAAC,UAAU;gBAC7B,eAAe,GAAG,IAAI,CAAC;gBACvB,UAAU,GAAG,6CAA6C,CAAC;gBAC3D,MAAM;YACR,KAAK,eAAe,CAAC,YAAY,CAAC;YAClC;gBACE,eAAe,GAAG,IAAI,CAAC;gBACvB,UAAU,GAAG,cAAc,CAAC;gBAC5B,MAAM;QACV,CAAC;QAED,sCAAsC;QACtC,MAAM,MAAM,GAAG,UAAU,CAAC,UAAU,CAAC,CAAC;QACtC,IAAI,iBAAiB,GAAG,UAAU,CAAC;QACnC,IAAI,MAAM,CAAC,aAAa,EAAE,CAAC;YACzB,iBAAiB,IAAI,UAAU,MAAM,CAAC,aAAa,GAAG,CAAC;QACzD,CAAC;QAED,uDAAuD;QACvD,OAAO,IAAI,GAAG,eAAe,aAAa,iBAAiB,eAAe,UAAU,EAAE,CAAC;QAEvF,IAAI,aAAa,GAAG,sBAAsB,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,KAAK,CAAC;QACpE,iCAAiC;QACjC,IAAI,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC;YAC3B,aAAa,GAAG,IAAI,CAAC;YACrB,IAAI,CAAC;gBACH,MAAM,EAAE,oBAAoB,EAAE,GAAG,MAAM,MAAM,CAAC,sBAAsB,CAAC,CAAC;gBACtE,MAAM,QAAQ,GAAG,MAAM,oBAAoB,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;gBACjE,IAAI,QAAQ,EAAE,CAAC;oBACb,MAAM,SAAS,GAAG,oBAAoB,CAAC,cAAc,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;oBACtE,IAAI,SAAS,EAAE,CAAC;wBACd,OAAO,IAAI,IAAI,YAAY,wBAAwB,WAAW,EAAE,CAAC;oBACnE,CAAC;yBAAM,CAAC;wBACN,OAAO,IAAI,IAAI,WAAW,wBAAwB,WAAW,EAAE,CAAC;wBAChE,aAAa,GAAG,KAAK,CAAC;oBACxB,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,OAAO,IAAI,IAAI,SAAS,4BAA4B,WAAW,EAAE,CAAC;gBACpE,CAAC;YACH,CAAC;YAAC,OAAO,IAAI,EAAE,CAAC;gBACd,gDAAgD;YAClD,CAAC;QACH,CAAC;QAED,4CAA4C;QAC5C,IAAI,MAAM,KAAK,eAAe,CAAC,SAAS,EAAE,CAAC;YACzC,OAAO,IAAI,KAAK,WAAW,CAAC,MAAM,SAAS,CAAC;QAC9C,CAAC;aAAM,IAAI,MAAM,KAAK,eAAe,CAAC,UAAU,EAAE,CAAC;YACjD,OAAO,IAAI,iCAAiC,CAAC;QAC/C,CAAC;aAAM,CAAC;YACN,OAAO,IAAI,KAAK,WAAW,CAAC,MAAM,gBAAgB,CAAC;QACrD,CAAC;QAED,yEAAyE;QACzE,IAAI,gBAAgB,IAAI,MAAM,EAAE,WAAW,EAAE,CAAC;YAC5C,MAAM,SAAS,GAAG,MAAM,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACxD,IAAI,SAAS,EAAE,CAAC;gBACd,OAAO,IAAI,KAAK,CAAC;gBACjB,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;oBACjC,OAAO,IAAI,OAAO,WAAW,GAAG,QAAQ,GAAG,WAAW,IAAI,CAAC;gBAC7D,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,OAAO,IAAI,IAAI,CAAC;YAClB,CAAC;QACH,CAAC;aAAM,CAAC;YACN,OAAO,IAAI,IAAI,CAAC;QAClB,CAAC;QAED,sCAAsC;QACtC,OAAO,IAAI,WAAW,CAAC;QAEvB,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC3B,WAAW,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;gBAC3B,IAAI,gBAAgB,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;oBACzC,wDAAwD;oBACxD,OAAO,IAAI,OAAO,UAAU,GAAG,IAAI,CAAC,IAAI,GAAG,WAAW,EAAE,CAAC;oBAEzD,iFAAiF;oBACjF,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;oBACtD,IAAI,SAAS,EAAE,CAAC;wBACd,OAAO,IAAI,KAAK,CAAC;wBACjB,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;4BACjC,OAAO,IAAI,SAAS,WAAW,GAAG,QAAQ,GAAG,WAAW,IAAI,CAAC;wBAC/D,CAAC;oBACH,CAAC;yBAAM,CAAC;wBACN,OAAO,IAAI,IAAI,CAAC;oBAClB,CAAC;oBACD,6CAA6C;gBAC/C,CAAC;qBAAM,CAAC;oBACN,sEAAsE;oBACtE,OAAO,IAAI,OAAO,UAAU,GAAG,IAAI,CAAC,IAAI,GAAG,WAAW,IAAI,CAAC;gBAC7D,CAAC;gBACD,MAAM,UAAU,GACd,IAAI,CAAC,MAAM,CAAC,oBAAoB,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;gBAC7D,IAAI,UAAU,IAAI,UAAU,EAAE,CAAC;oBAC7B,gCAAgC;oBAChC,OAAO,IAAI,OAAO,UAAU,cAAc,WAAW,IAAI,CAAC;oBAE1D,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC;yBACpD,IAAI,EAAE;yBACN,KAAK,CAAC,IAAI,CAAC,CAAC;oBACf,IAAI,WAAW,EAAE,CAAC;wBAChB,KAAK,MAAM,UAAU,IAAI,WAAW,EAAE,CAAC;4BACrC,OAAO,IAAI,SAAS,WAAW,GAAG,UAAU,GAAG,WAAW,IAAI,CAAC;wBACjE,CAAC;oBACH,CAAC;gBACH,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,OAAO,IAAI,sBAAsB,CAAC;YAClC,IAAI,MAAM,KAAK,eAAe,CAAC,YAAY,IAAI,aAAa,EAAE,CAAC;gBAC7D,OAAO,IAAI,IAAI,UAAU,qBAAqB,UAAU,iCAAiC,WAAW,EAAE,CAAC;YACzG,CAAC;YACD,OAAO,IAAI,IAAI,CAAC;QAClB,CAAC;QACD,OAAO,IAAI,IAAI,CAAC;IAClB,CAAC;IAED,KAAK,MAAM,MAAM,IAAI,iBAAiB,EAAE,CAAC;QACvC,IAAI,iBAAiB,GAAG,MAAM,CAAC,IAAI,CAAC;QACpC,IAAI,MAAM,CAAC,aAAa,EAAE,CAAC;YACzB,iBAAiB,IAAI,UAAU,MAAM,CAAC,aAAa,GAAG,CAAC;QACzD,CAAC;QACD,OAAO,IAAI,eAAe,iBAAiB,yBAAyB,CAAC;IACvE,CAAC;IAED,kDAAkD;IAClD,IAAI,QAAQ,EAAE,CAAC;QACb,OAAO,IAAI,IAAI,CAAC;QAChB,OAAO,IAAI,GAAG,UAAU,WAAW,WAAW,IAAI,CAAC;QACnD,OAAO,IAAI,WAAW,UAAU,YAAY,WAAW,yCAAyC,CAAC;QACjG,OAAO,IAAI,WAAW,UAAU,cAAc,WAAW,mCAAmC,CAAC;QAC7F,OAAO,IAAI,WAAW,UAAU,cAAc,WAAW,yBAAyB,CAAC;QACnF,OAAO,IAAI,WAAW,UAAU,0BAA0B,WAAW,+CAA+C,CAAC;QACrH,OAAO,IAAI,aAAa,UAAU,SAAS,WAAW,uCAAuC,CAAC;QAC9F,OAAO,IAAI,IAAI,CAAC;IAClB,CAAC;IAED,8FAA8F;IAC9F,OAAO,IAAI,WAAW,CAAC;IAEvB,OAAO;QACL,IAAI,EAAE,SAAS;QACf,WAAW,EAAE,MAAM;QACnB,OAAO,EAAE,OAAO;KACjB,CAAC;AACJ,CAAC,CAAC;AAEF,MAAM,WAAW,GAAiB;IAChC,IAAI,EAAE,MAAM;IACZ,WAAW,EAAE,+CAA+C;IAC5D,IAAI,EAAE,WAAW,CAAC,QAAQ;IAC1B,MAAM,EAAE,KAAK,EACX,OAAuB,EACvB,IAAY,EACkB,EAAE;QAChC,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;QAC/B,MAAM,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC,QAAQ,CAAC;QAEpC,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,OAAO;gBACL,IAAI,EAAE,SAAS;gBACf,WAAW,EAAE,OAAO;gBACpB,OAAO,EAAE,oBAAoB;aAC9B,CAAC;QACJ,CAAC;QAED,MAAM,UAAU,GAAG,MAAM,CAAC,aAAa,EAAE,IAAI,EAAE,CAAC;QAEhD,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,kCAAkC;YAClC,MAAM,YAAY,GAAG,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC;iBAC5C,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC,KAAK,EAAE,OAAO,CAAC;iBAC9C,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC;YAE5B,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC9B,OAAO;oBACL,IAAI,EAAE,SAAS;oBACf,WAAW,EAAE,MAAM;oBACnB,OAAO,EAAE,sDAAsD;iBAChE,CAAC;YACJ,CAAC;YAED,OAAO;gBACL,IAAI,EAAE,SAAS;gBACf,WAAW,EAAE,MAAM;gBACnB,OAAO,EAAE,2CAA2C,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,kDAAkD;aACrJ,CAAC;QACJ,CAAC;QAED,MAAM,MAAM,GAAG,UAAU,CAAC,UAAU,CAAC,CAAC;QACtC,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,OAAO;gBACL,IAAI,EAAE,SAAS;gBACf,WAAW,EAAE,OAAO;gBACpB,OAAO,EAAE,eAAe,UAAU,cAAc;aACjD,CAAC;QACJ,CAAC;QAED,yEAAyE;QACzE,4EAA4E;QAE5E,IAAI,CAAC;YACH,OAAO,CAAC,EAAE,CAAC,OAAO,CAChB;gBACE,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE,iDAAiD,UAAU,MAAM;aACxE,EACD,IAAI,CAAC,GAAG,EAAE,CACX,CAAC;YAEF,oDAAoD;YACpD,MAAM,EAAE,gBAAgB,EAAE,GAAG,MAAM,MAAM,CAAC,sBAAsB,CAAC,CAAC;YAElE,4EAA4E;YAC5E,MAAM,WAAW,GAAG,MAAM,CAAC,KAAK,IAAI;gBAClC,gBAAgB,EAAE,EAAE,EAAE,mCAAmC;gBACzD,QAAQ,EAAE,EAAE,EAAE,mCAAmC;aAClD,CAAC;YAEF,8CAA8C;YAC9C,MAAM,YAAY,GAAG,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,GAAG,CAAC;YAClD,MAAM,gBAAgB,CAAC,YAAY,CACjC,UAAU,EACV,WAAW,EACX,YAAY,CACb,CAAC;YAEF,OAAO,CAAC,EAAE,CAAC,OAAO,CAChB;gBACE,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE,iDAAiD,UAAU,IAAI;aACtE,EACD,IAAI,CAAC,GAAG,EAAE,CACX,CAAC;YAEF,4DAA4D;YAC5D,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,eAAe,EAAE,CAAC;YACpD,IAAI,YAAY,EAAE,CAAC;gBACjB,OAAO,CAAC,EAAE,CAAC,OAAO,CAChB;oBACE,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,8BAA8B,UAAU,MAAM;iBACrD,EACD,IAAI,CAAC,GAAG,EAAE,CACX,CAAC;gBACF,MAAM,YAAY,CAAC,sBAAsB,CAAC,UAAU,CAAC,CAAC;YACxD,CAAC;YACD,uCAAuC;YACvC,MAAM,YAAY,GAAG,MAAM,CAAC,eAAe,EAAE,CAAC;YAC9C,IAAI,YAAY,EAAE,CAAC;gBACjB,MAAM,YAAY,CAAC,QAAQ,EAAE,CAAC;YAChC,CAAC;YAED,OAAO;gBACL,IAAI,EAAE,SAAS;gBACf,WAAW,EAAE,MAAM;gBACnB,OAAO,EAAE,uDAAuD,UAAU,IAAI;aAC/E,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,IAAI,EAAE,SAAS;gBACf,WAAW,EAAE,OAAO;gBACpB,OAAO,EAAE,2CAA2C,UAAU,MAAM,eAAe,CAAC,KAAK,CAAC,EAAE;aAC7F,CAAC;QACJ,CAAC;IACH,CAAC;IACD,UAAU,EAAE,KAAK,EAAE,OAAuB,EAAE,UAAkB,EAAE,EAAE;QAChE,MAAM,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC,QAAQ,CAAC;QACpC,IAAI,CAAC,MAAM;YAAE,OAAO,EAAE,CAAC;QAEvB,MAAM,UAAU,GAAG,MAAM,CAAC,aAAa,EAAE,IAAI,EAAE,CAAC;QAChD,OAAO,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAC7C,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,CAC5B,CAAC;IACJ,CAAC;CACF,CAAC;AAEF,MAAM,WAAW,GAAiB;IAChC,IAAI,EAAE,MAAM;IACZ,WAAW,EAAE,uCAAuC;IACpD,IAAI,EAAE,WAAW,CAAC,QAAQ;IAC1B,MAAM,EAAE,KAAK,EAAE,OAAuB,EAAE,IAAY,EAAE,EAAE;QACtD,MAAM,aAAa,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAEtE,MAAM,OAAO,GACX,aAAa,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,aAAa,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;QAC3E,MAAM,SAAS,GACb,aAAa,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAChC,aAAa,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;QAC3C,MAAM,UAAU,GAAG,aAAa,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAEpD,sDAAsD;QACtD,mDAAmD;QACnD,MAAM,gBAAgB,GAAG,CAAC,SAAS,IAAI,CAAC,OAAO,IAAI,UAAU,CAAC,CAAC;QAE/D,gDAAgD;QAChD,MAAM,QAAQ,GAAG,aAAa,CAAC,MAAM,KAAK,CAAC,CAAC;QAE5C,OAAO,YAAY,CAAC,OAAO,EAAE,gBAAgB,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;IACvE,CAAC;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,UAAU,GAAiB;IACtC,IAAI,EAAE,KAAK;IACX,WAAW,EACT,mFAAmF;IACrF,IAAI,EAAE,WAAW,CAAC,QAAQ;IAC1B,WAAW,EAAE,CAAC,WAAW,EAAE,WAAW,CAAC;IACvC,gDAAgD;IAChD,MAAM,EAAE,KAAK,EAAE,OAAuB,EAAE,IAAY,EAAE,EAAE;IACtD,yCAAyC;IACzC,WAAW,CAAC,MAAO,CAAC,OAAO,EAAE,IAAI,CAAC;CACrC,CAAC"}